# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('src', 'src'), ('assets', 'assets'), ('config', 'config')],
    hiddenimports=['PyQt5', 'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.QtTest', 'selenium', 'selenium.webdriver', 'selenium.webdriver.chrome', 'selenium.webdriver.chrome.service', 'selenium.webdriver.chrome.options', 'selenium.webdriver.common', 'selenium.webdriver.common.by', 'selenium.webdriver.support', 'selenium.webdriver.support.ui', 'selenium.webdriver.support.expected_conditions', 'selenium.common', 'selenium.common.exceptions', 'requests', 'requests.adapters', 'requests.auth', 'requests.cookies', 'requests.exceptions', 'cryptography', 'cryptography.fernet', 'cryptography.hazmat', 'cryptography.hazmat.primitives', 'cryptography.hazmat.primitives.hashes', 'cryptography.hazmat.primitives.kdf', 'cryptography.hazmat.primitives.kdf.pbkdf2', 'cryptography.hazmat.backends', 'cryptography.hazmat.backends.openssl', 'bcrypt', 'webdriver_manager', 'webdriver_manager.chrome', 'webdriver_manager.core', 'psutil', 'loguru', 'configparser', 'pathlib', 'json', 'logging', 'logging.handlers', 'logging.config', 'threading', 'queue', 'time', 'os', 'sys', 'base64', 'hashlib', 'secrets', 'functools', 'typing', 'dataclasses', 'enum', 'abc', 'datetime', 'traceback', 'urllib', 'urllib.parse', 'urllib.request', 'urllib.error', 'http', 'http.client', 'http.server', 'random', 're', 'socket', 'ssl', 'platform', 'subprocess', 'shutil', 'tempfile', 'zipfile', 'collections', 'itertools', 'copy', 'pickle', 'struct', 'math', 'statistics', 'decimal', 'fractions', 'operator', 'weakref', 'gc', 'inspect', 'importlib', 'importlib.util', 'pkgutil', 'warnings', 'contextlib', 'atexit', 'signal', 'getpass', 'locale', 'codecs', 'encodings', 'encodings.utf_8', 'encodings.latin_1', 'encodings.ascii', 'io', 'mmap', 'select', 'errno', 'stat', 'glob', 'fnmatch', 'linecache', 'tokenize', 'keyword', 'ast', 'dis', 'code', 'codeop', 'py_compile', 'compileall', 'zipimport', 'runpy', 'types', 'builtins'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='TikTokAutomationTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
