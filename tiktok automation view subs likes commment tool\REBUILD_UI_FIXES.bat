@echo off
title TikTok Automation Tool - UI Fixes Rebuild
color 0A
echo.
echo ████████╗██╗██╗  ██╗████████╗ ██████╗ ██╗  ██╗
echo ╚══██╔══╝██║██║ ██╔╝╚══██╔══╝██╔═══██╗██║ ██╔╝
echo    ██║   ██║█████╔╝    ██║   ██║   ██║█████╔╝ 
echo    ██║   ██║██╔═██╗    ██║   ██║   ██║██╔═██╗ 
echo    ██║   ██║██║  ██╗   ██║   ╚██████╔╝██║  ██╗
echo    ╚═╝   ╚═╝╚═╝  ╚═╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝
echo.
echo         REBUILDING WITH UI FIXES
echo ================================================
echo.
echo ✅ FIXED ISSUES:
echo    • Login dialog now resizable (600x700)
echo    • Input fields much wider (350px minimum)
echo    • Better spacing between elements
echo    • Larger text and padding in input fields
echo    • Main window already resizable
echo.

echo [1/3] Cleaning previous build...
if exist build rmdir /s /q build >nul 2>&1
if exist dist rmdir /s /q dist >nul 2>&1
echo ✅ Cleanup complete

echo.
echo [2/3] Building with UI fixes...
echo This will take 5-10 minutes...
echo.

call build_exe.bat

echo.
echo [3/3] Checking result...
if exist "dist\TikTokAutomationTool.exe" (
    echo.
    echo ████████████████████████████████████████████████████
    echo                🎉 UI FIXES APPLIED! 🎉
    echo ████████████████████████████████████████████████████
    echo.
    echo ✅ New executable: dist\TikTokAutomationTool.exe
    echo.
    echo 🔧 UI IMPROVEMENTS:
    echo    ✅ Login dialog is now resizable
    echo    ✅ Input fields are much wider
    echo    ✅ Better spacing and layout
    echo    ✅ Larger text for better readability
    echo    ✅ Main window is resizable
    echo.
    echo 🚀 TEST THE NEW VERSION:
    echo    1. Close the old version if running
    echo    2. Run the new TikTokAutomationTool.exe
    echo    3. Try resizing the login dialog
    echo    4. Check if input fields are now usable
    echo.
    echo Opening dist folder...
    explorer dist
) else (
    echo.
    echo ❌ BUILD FAILED
    echo Check the error messages above
    echo.
)

echo.
echo Press any key to exit...
pause >nul
