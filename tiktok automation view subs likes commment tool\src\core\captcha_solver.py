"""
Captcha Solver for TikTok Automation Tool
Integrates with external captcha solving services.
"""

import time
import logging
import base64
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod
import requests
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class CaptchaSolverBase(ABC):
    """Base class for captcha solvers."""
    
    def __init__(self, api_key: str, config: Dict = None):
        """Initialize captcha solver.
        
        Args:
            api_key: API key for the captcha service
            config: Additional configuration
        """
        self.api_key = api_key
        self.config = config or {}
    
    @abstractmethod
    def solve_image_captcha(self, image_data: bytes) -> Optional[str]:
        """Solve image-based captcha.
        
        Args:
            image_data: Image data as bytes
            
        Returns:
            Solved captcha text or None if failed
        """
        pass
    
    @abstractmethod
    def solve_recaptcha_v2(self, site_key: str, page_url: str) -> Optional[str]:
        """Solve reCAPTCHA v2.
        
        Args:
            site_key: reCAPTCHA site key
            page_url: Page URL where captcha appears
            
        Returns:
            Solved captcha token or None if failed
        """
        pass

class TwoCaptchaSolver(CaptchaSolverBase):
    """2captcha.com captcha solver."""
    
    def __init__(self, api_key: str, config: Dict = None):
        """Initialize 2captcha solver."""
        super().__init__(api_key, config)
        self.base_url = "http://2captcha.com"
        self.timeout = config.get('timeout', 120) if config else 120
    
    def solve_image_captcha(self, image_data: bytes) -> Optional[str]:
        """Solve image captcha using 2captcha."""
        try:
            # Submit captcha
            submit_url = f"{self.base_url}/in.php"
            
            files = {'file': ('captcha.png', image_data, 'image/png')}
            data = {
                'key': self.api_key,
                'method': 'post'
            }
            
            response = requests.post(submit_url, files=files, data=data)
            
            if response.text.startswith('OK|'):
                captcha_id = response.text.split('|')[1]
                
                # Wait for solution
                return self._wait_for_solution(captcha_id)
            else:
                logging.error(f"2captcha submit failed: {response.text}")
                return None
        
        except Exception as e:
            logging.error(f"2captcha image solve error: {e}")
            return None
    
    def solve_recaptcha_v2(self, site_key: str, page_url: str) -> Optional[str]:
        """Solve reCAPTCHA v2 using 2captcha."""
        try:
            # Submit reCAPTCHA
            submit_url = f"{self.base_url}/in.php"
            
            data = {
                'key': self.api_key,
                'method': 'userrecaptcha',
                'googlekey': site_key,
                'pageurl': page_url
            }
            
            response = requests.post(submit_url, data=data)
            
            if response.text.startswith('OK|'):
                captcha_id = response.text.split('|')[1]
                
                # Wait for solution
                return self._wait_for_solution(captcha_id)
            else:
                logging.error(f"2captcha reCAPTCHA submit failed: {response.text}")
                return None
        
        except Exception as e:
            logging.error(f"2captcha reCAPTCHA solve error: {e}")
            return None
    
    def _wait_for_solution(self, captcha_id: str) -> Optional[str]:
        """Wait for captcha solution."""
        result_url = f"{self.base_url}/res.php"
        
        start_time = time.time()
        while time.time() - start_time < self.timeout:
            try:
                response = requests.get(result_url, params={
                    'key': self.api_key,
                    'action': 'get',
                    'id': captcha_id
                })
                
                if response.text == 'CAPCHA_NOT_READY':
                    time.sleep(5)
                    continue
                elif response.text.startswith('OK|'):
                    solution = response.text.split('|')[1]
                    logging.info(f"2captcha solved: {captcha_id}")
                    return solution
                else:
                    logging.error(f"2captcha solution error: {response.text}")
                    return None
            
            except Exception as e:
                logging.error(f"2captcha result check error: {e}")
                time.sleep(5)
        
        logging.error(f"2captcha timeout for: {captcha_id}")
        return None

class AntiCaptchaSolver(CaptchaSolverBase):
    """Anti-Captcha.com captcha solver."""
    
    def __init__(self, api_key: str, config: Dict = None):
        """Initialize Anti-Captcha solver."""
        super().__init__(api_key, config)
        self.base_url = "https://api.anti-captcha.com"
        self.timeout = config.get('timeout', 120) if config else 120
    
    def solve_image_captcha(self, image_data: bytes) -> Optional[str]:
        """Solve image captcha using Anti-Captcha."""
        try:
            # Encode image to base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # Submit captcha
            submit_data = {
                "clientKey": self.api_key,
                "task": {
                    "type": "ImageToTextTask",
                    "body": image_base64
                }
            }
            
            response = requests.post(f"{self.base_url}/createTask", json=submit_data)
            result = response.json()
            
            if result.get('errorId') == 0:
                task_id = result['taskId']
                
                # Wait for solution
                return self._wait_for_solution(task_id)
            else:
                logging.error(f"Anti-Captcha submit failed: {result}")
                return None
        
        except Exception as e:
            logging.error(f"Anti-Captcha image solve error: {e}")
            return None
    
    def solve_recaptcha_v2(self, site_key: str, page_url: str) -> Optional[str]:
        """Solve reCAPTCHA v2 using Anti-Captcha."""
        try:
            # Submit reCAPTCHA
            submit_data = {
                "clientKey": self.api_key,
                "task": {
                    "type": "NoCaptchaTaskProxyless",
                    "websiteURL": page_url,
                    "websiteKey": site_key
                }
            }
            
            response = requests.post(f"{self.base_url}/createTask", json=submit_data)
            result = response.json()
            
            if result.get('errorId') == 0:
                task_id = result['taskId']
                
                # Wait for solution
                return self._wait_for_solution(task_id)
            else:
                logging.error(f"Anti-Captcha reCAPTCHA submit failed: {result}")
                return None
        
        except Exception as e:
            logging.error(f"Anti-Captcha reCAPTCHA solve error: {e}")
            return None
    
    def _wait_for_solution(self, task_id: int) -> Optional[str]:
        """Wait for captcha solution."""
        start_time = time.time()
        while time.time() - start_time < self.timeout:
            try:
                response = requests.post(f"{self.base_url}/getTaskResult", json={
                    "clientKey": self.api_key,
                    "taskId": task_id
                })
                
                result = response.json()
                
                if result.get('status') == 'ready':
                    solution = result['solution']['text']
                    logging.info(f"Anti-Captcha solved: {task_id}")
                    return solution
                elif result.get('status') == 'processing':
                    time.sleep(5)
                    continue
                else:
                    logging.error(f"Anti-Captcha solution error: {result}")
                    return None
            
            except Exception as e:
                logging.error(f"Anti-Captcha result check error: {e}")
                time.sleep(5)
        
        logging.error(f"Anti-Captcha timeout for: {task_id}")
        return None

class CaptchaSolver:
    """Main captcha solver that manages different services."""
    
    def __init__(self, config: Dict):
        """Initialize captcha solver.
        
        Args:
            config: Captcha configuration
        """
        self.config = config
        self.solver = None
        
        # Initialize solver based on config
        service = config.get('service', '').lower()
        api_key = config.get('api_key', '')
        
        if service == '2captcha' and api_key:
            self.solver = TwoCaptchaSolver(api_key, config)
        elif service == 'anti-captcha' and api_key:
            self.solver = AntiCaptchaSolver(api_key, config)
        
        if self.solver:
            logging.info(f"Initialized captcha solver: {service}")
        else:
            logging.info("No captcha solver configured")
    
    def is_enabled(self) -> bool:
        """Check if captcha solver is enabled.
        
        Returns:
            True if solver is available
        """
        return self.solver is not None
    
    def solve_captcha_on_page(self, driver, timeout: int = 30) -> bool:
        """Automatically detect and solve captcha on current page.
        
        Args:
            driver: Selenium WebDriver instance
            timeout: Timeout in seconds
            
        Returns:
            True if captcha was solved or not found, False if failed
        """
        if not self.solver:
            return True  # No solver configured, assume no captcha
        
        try:
            # Look for common captcha elements
            captcha_selectors = [
                'iframe[src*="recaptcha"]',
                '.g-recaptcha',
                '.captcha-container',
                'img[alt*="captcha"]',
                '[data-captcha]'
            ]
            
            captcha_element = None
            for selector in captcha_selectors:
                try:
                    captcha_element = WebDriverWait(driver, 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue
            
            if not captcha_element:
                # No captcha found
                return True
            
            logging.info("Captcha detected, attempting to solve...")
            
            # Handle reCAPTCHA
            if 'recaptcha' in captcha_element.get_attribute('src') or 'g-recaptcha' in captcha_element.get_attribute('class'):
                return self._solve_recaptcha(driver, captcha_element)
            
            # Handle image captcha
            elif captcha_element.tag_name == 'img':
                return self._solve_image_captcha(driver, captcha_element)
            
            logging.warning("Unknown captcha type detected")
            return False
        
        except Exception as e:
            logging.error(f"Captcha solving error: {e}")
            return False
    
    def _solve_recaptcha(self, driver, captcha_element) -> bool:
        """Solve reCAPTCHA on page."""
        try:
            # Get site key and page URL
            site_key = driver.execute_script(
                "return window.___grecaptcha_cfg && window.___grecaptcha_cfg.clients && "
                "Object.keys(window.___grecaptcha_cfg.clients)[0] && "
                "window.___grecaptcha_cfg.clients[Object.keys(window.___grecaptcha_cfg.clients)[0]].sitekey"
            )
            
            if not site_key:
                logging.error("Could not find reCAPTCHA site key")
                return False
            
            page_url = driver.current_url
            
            # Solve captcha
            solution = self.solver.solve_recaptcha_v2(site_key, page_url)
            
            if solution:
                # Inject solution
                driver.execute_script(f"document.getElementById('g-recaptcha-response').innerHTML = '{solution}';")
                driver.execute_script("if(typeof ___grecaptcha_cfg !== 'undefined') { ___grecaptcha_cfg.clients[0].callback(arguments[0]); }", solution)
                
                logging.info("reCAPTCHA solution injected")
                return True
            
            return False
        
        except Exception as e:
            logging.error(f"reCAPTCHA solving error: {e}")
            return False
    
    def _solve_image_captcha(self, driver, captcha_element) -> bool:
        """Solve image captcha on page."""
        try:
            # Get captcha image
            image_data = captcha_element.screenshot_as_png
            
            # Solve captcha
            solution = self.solver.solve_image_captcha(image_data)
            
            if solution:
                # Find input field and enter solution
                input_selectors = [
                    'input[name*="captcha"]',
                    'input[id*="captcha"]',
                    '.captcha-input',
                    'input[type="text"]'
                ]
                
                for selector in input_selectors:
                    try:
                        input_field = driver.find_element(By.CSS_SELECTOR, selector)
                        input_field.clear()
                        input_field.send_keys(solution)
                        
                        logging.info("Image captcha solution entered")
                        return True
                    except:
                        continue
                
                logging.error("Could not find captcha input field")
                return False
            
            return False
        
        except Exception as e:
            logging.error(f"Image captcha solving error: {e}")
            return False
