/* TikTok Automation Tool - Professional Theme Stylesheet */

/* Main Application */
QMainWindow {
    background-color: #000000;
    color: #FFFFFF;
    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
}

QWidget {
    background-color: #000000;
    color: #FFFFFF;
    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
}

/* Buttons */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #FF0050, stop:1 #E6004A);
    color: #FFFFFF;
    border: none;
    border-radius: 12px;
    padding: 14px 28px;
    font-size: 14px;
    font-weight: 600;
    min-height: 24px;
    text-align: center;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #FF1A5C, stop:1 #F0005A);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 0, 80, 0.3);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #CC0042, stop:1 #B8003A);
    transform: translateY(0px);
}

QPushButton:disabled {
    background-color: #2F2F35;
    color: #A8A8B3;
}

/* Secondary Buttons */
QPushButton[class="secondary"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #25F4EE, stop:1 #1FD4C9);
    color: #000000;
    font-weight: 700;
}

QPushButton[class="secondary"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3FFFF5, stop:1 #2EEEE5);
}

/* Input Fields */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #161823;
    border: 2px solid #2F2F35;
    border-radius: 10px;
    padding: 14px 16px;
    color: #FFFFFF;
    font-size: 14px;
    selection-background-color: #FF0050;
    selection-color: #FFFFFF;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 2px solid #FF0050;
    background-color: #1A1A2E;
    outline: none;
    box-shadow: 0 0 10px rgba(255, 0, 80, 0.2);
}

QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
    border-color: #25F4EE;
}

/* ComboBox */
QComboBox {
    background-color: #161823;
    border: 2px solid #2F2F35;
    border-radius: 10px;
    padding: 14px 16px;
    color: #FFFFFF;
    font-size: 14px;
    min-width: 140px;
}

QComboBox:hover {
    border-color: #FF0050;
}

QComboBox:focus {
    border-color: #25F4EE;
}

QComboBox::drop-down {
    border: none;
    width: 32px;
    background: transparent;
}

QComboBox::down-arrow {
    image: none;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid #FFFFFF;
    margin-right: 8px;
}

QComboBox QAbstractItemView {
    background-color: #161823;
    border: 2px solid #2F2F35;
    border-radius: 8px;
    color: #FFFFFF;
    selection-background-color: #FF0050;
    selection-color: #FFFFFF;
    outline: none;
}

/* Spin Boxes */
QSpinBox, QDoubleSpinBox {
    background-color: #161823;
    border: 2px solid #2F2F35;
    border-radius: 10px;
    padding: 14px 16px;
    color: #FFFFFF;
    font-size: 14px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #FF0050;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    background-color: #FF0050;
    border: none;
    border-radius: 4px;
    width: 20px;
    margin: 2px;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #FF0050;
    border: none;
    border-radius: 4px;
    width: 20px;
    margin: 2px;
}

/* Progress Bars */
QProgressBar {
    background-color: #161823;
    border: 2px solid #2F2F35;
    border-radius: 10px;
    text-align: center;
    color: #FFFFFF;
    font-weight: 600;
    font-size: 12px;
    min-height: 24px;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #FF0050, stop:0.5 #FF4081, stop:1 #25F4EE);
    border-radius: 8px;
    margin: 2px;
}

/* Labels */
QLabel {
    color: #FFFFFF;
    font-size: 14px;
    font-weight: 500;
}

QLabel[class="title"] {
    font-size: 28px;
    font-weight: 700;
    color: #FF0050;
    margin: 8px 0;
}

QLabel[class="subtitle"] {
    font-size: 18px;
    font-weight: 600;
    color: #25F4EE;
    margin: 4px 0;
}

QLabel[class="description"] {
    color: #A8A8B3;
    font-size: 12px;
    font-weight: 400;
    line-height: 1.4;
}

/* Group Boxes */
QGroupBox {
    background-color: #161823;
    border: 2px solid #2F2F35;
    border-radius: 16px;
    margin-top: 16px;
    padding-top: 16px;
    font-weight: 600;
    font-size: 14px;
    color: #FFFFFF;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 16px;
    padding: 0 12px 0 12px;
    color: #FF0050;
    font-weight: 700;
    background-color: #000000;
}

/* Tab Widget */
QTabWidget::pane {
    background-color: #161823;
    border: 2px solid #2F2F35;
    border-radius: 12px;
    margin-top: 8px;
}

QTabBar::tab {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #2F2F35, stop:1 #252530);
    color: #A8A8B3;
    padding: 14px 28px;
    margin-right: 4px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    font-weight: 600;
    font-size: 13px;
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #FF0050, stop:1 #E6004A);
    color: #FFFFFF;
}

QTabBar::tab:hover:!selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #25F4EE, stop:1 #1FD4C9);
    color: #000000;
}

/* Checkboxes */
QCheckBox {
    color: #FFFFFF;
    font-size: 14px;
    font-weight: 500;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
    border: 2px solid #2F2F35;
    border-radius: 6px;
    background-color: #161823;
}

QCheckBox::indicator:hover {
    border-color: #FF0050;
}

QCheckBox::indicator:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #FF0050, stop:1 #25F4EE);
    border-color: #FF0050;
}

QCheckBox::indicator:checked:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #FF1A5C, stop:1 #3FFFF5);
}

/* Scroll Bars */
QScrollBar:vertical {
    background-color: #161823;
    width: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #FF0050, stop:1 #25F4EE);
    border-radius: 7px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #FF1A5C, stop:1 #3FFFF5);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #161823;
    height: 14px;
    border-radius: 7px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #FF0050, stop:1 #25F4EE);
    border-radius: 7px;
    min-width: 30px;
    margin: 2px;
}

/* Menu Bar */
QMenuBar {
    background-color: #161823;
    color: #FFFFFF;
    border-bottom: 2px solid #2F2F35;
    font-weight: 500;
}

QMenuBar::item {
    padding: 12px 20px;
    background-color: transparent;
    border-radius: 6px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #FF0050;
    color: #FFFFFF;
}

QMenuBar::item:pressed {
    background-color: #E6004A;
}

/* Status Bar */
QStatusBar {
    background-color: #161823;
    color: #A8A8B3;
    border-top: 2px solid #2F2F35;
    font-size: 12px;
    padding: 4px 8px;
}

/* Tool Tips */
QToolTip {
    background-color: #1F1F23;
    color: #FFFFFF;
    border: 2px solid #FF0050;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
}

/* Splitter */
QSplitter::handle {
    background-color: #2F2F35;
    border-radius: 2px;
}

QSplitter::handle:horizontal {
    width: 4px;
    margin: 2px 0;
}

QSplitter::handle:vertical {
    height: 4px;
    margin: 0 2px;
}

QSplitter::handle:hover {
    background-color: #FF0050;
}

/* Frame */
QFrame {
    background-color: transparent;
    border: none;
}

QFrame[frameShape="1"] { /* Box frame */
    border: 2px solid #2F2F35;
    border-radius: 8px;
}

/* Animations and Effects */
QPushButton {
    transition: all 0.2s ease-in-out;
}

QLineEdit, QTextEdit, QPlainTextEdit {
    transition: border-color 0.2s ease-in-out;
}

/* Custom Classes */
.success-text {
    color: #00D084;
    font-weight: 600;
}

.error-text {
    color: #FF3040;
    font-weight: 600;
}

.warning-text {
    color: #FFB800;
    font-weight: 600;
}

.accent-text {
    color: #8A2BE2;
    font-weight: 600;
}
