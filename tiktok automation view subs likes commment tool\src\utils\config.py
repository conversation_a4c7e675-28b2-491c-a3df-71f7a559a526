"""
Configuration Manager for TikTok Automation Tool
Handles application settings, user preferences, and persistent storage.
"""

import os
import json
import configparser
from pathlib import Path
from typing import Any, Dict, Optional
import logging

class Config:
    """Configuration manager for the application."""
    
    def __init__(self, config_dir: Path):
        """Initialize configuration manager.
        
        Args:
            config_dir: Directory to store configuration files
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # Configuration files
        self.settings_file = self.config_dir / "settings.ini"
        self.user_data_file = self.config_dir / "user_data.json"
        self.automation_file = self.config_dir / "automation_settings.json"
        
        # Initialize configuration parsers
        self.settings = configparser.ConfigParser()
        self.user_data = {}
        self.automation_settings = {}
        
        # Load existing configuration
        self.load_all()
        
        # Setup default configuration
        self.setup_defaults()
    
    def setup_defaults(self):
        """Setup default configuration values."""
        # Application settings
        if not self.settings.has_section('app'):
            self.settings.add_section('app')
        
        defaults = {
            'app': {
                'version': '1.0.0',
                'disclaimer_accepted': 'False',
                'first_run': 'True',
                'auto_update': 'True',
                'language': 'en'
            },
            'ui': {
                'theme': 'dark',
                'window_width': '1200',
                'window_height': '800',
                'window_maximized': 'False',
                'show_tooltips': 'True',
                'animation_enabled': 'True'
            },
            'automation': {
                'default_delay': '2',
                'max_retries': '3',
                'timeout': '30',
                'headless_mode': 'False',
                'use_proxy': 'False',
                'captcha_service': 'none'
            },
            'security': {
                'encrypt_data': 'True',
                'session_timeout': '3600',
                'auto_logout': 'True',
                'secure_mode': 'True'
            },
            'logging': {
                'level': 'INFO',
                'max_file_size': '10485760',  # 10MB
                'backup_count': '5',
                'console_output': 'True'
            }
        }
        
        for section, options in defaults.items():
            if not self.settings.has_section(section):
                self.settings.add_section(section)
            
            for key, value in options.items():
                if not self.settings.has_option(section, key):
                    self.settings.set(section, key, value)
    
    def load_all(self):
        """Load all configuration files."""
        self.load_settings()
        self.load_user_data()
        self.load_automation_settings()
    
    def load_settings(self):
        """Load settings from INI file."""
        if self.settings_file.exists():
            try:
                self.settings.read(self.settings_file)
                logging.info("Settings loaded successfully")
            except Exception as e:
                logging.error(f"Error loading settings: {e}")
    
    def load_user_data(self):
        """Load user data from JSON file."""
        if self.user_data_file.exists():
            try:
                with open(self.user_data_file, 'r', encoding='utf-8') as f:
                    self.user_data = json.load(f)
                logging.info("User data loaded successfully")
            except Exception as e:
                logging.error(f"Error loading user data: {e}")
                self.user_data = {}
    
    def load_automation_settings(self):
        """Load automation settings from JSON file."""
        if self.automation_file.exists():
            try:
                with open(self.automation_file, 'r', encoding='utf-8') as f:
                    self.automation_settings = json.load(f)
                logging.info("Automation settings loaded successfully")
            except Exception as e:
                logging.error(f"Error loading automation settings: {e}")
                self.automation_settings = {}
    
    def save_all(self):
        """Save all configuration files."""
        self.save_settings()
        self.save_user_data()
        self.save_automation_settings()
    
    def save_settings(self):
        """Save settings to INI file."""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                self.settings.write(f)
            logging.info("Settings saved successfully")
        except Exception as e:
            logging.error(f"Error saving settings: {e}")
    
    def save_user_data(self):
        """Save user data to JSON file."""
        try:
            with open(self.user_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_data, f, indent=2, ensure_ascii=False)
            logging.info("User data saved successfully")
        except Exception as e:
            logging.error(f"Error saving user data: {e}")
    
    def save_automation_settings(self):
        """Save automation settings to JSON file."""
        try:
            with open(self.automation_file, 'w', encoding='utf-8') as f:
                json.dump(self.automation_settings, f, indent=2, ensure_ascii=False)
            logging.info("Automation settings saved successfully")
        except Exception as e:
            logging.error(f"Error saving automation settings: {e}")
    
    def get(self, section: str, key: str, fallback: Any = None) -> Any:
        """Get configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            fallback: Default value if key not found
            
        Returns:
            Configuration value
        """
        try:
            if self.settings.has_option(section, key):
                value = self.settings.get(section, key)
                
                # Convert boolean strings
                if value.lower() in ('true', 'false'):
                    return value.lower() == 'true'
                
                # Try to convert to int
                try:
                    return int(value)
                except ValueError:
                    pass
                
                # Try to convert to float
                try:
                    return float(value)
                except ValueError:
                    pass
                
                return value
            
            return fallback
            
        except Exception as e:
            logging.error(f"Error getting config value {section}.{key}: {e}")
            return fallback
    
    def set(self, section: str, key: str, value: Any):
        """Set configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            value: Value to set
        """
        try:
            if not self.settings.has_section(section):
                self.settings.add_section(section)
            
            self.settings.set(section, key, str(value))
            
        except Exception as e:
            logging.error(f"Error setting config value {section}.{key}: {e}")
    
    def save(self):
        """Save current configuration."""
        self.save_all()
    
    def get_user_data(self, key: str, default: Any = None) -> Any:
        """Get user data value."""
        return self.user_data.get(key, default)
    
    def set_user_data(self, key: str, value: Any):
        """Set user data value."""
        self.user_data[key] = value
    
    def get_automation_setting(self, key: str, default: Any = None) -> Any:
        """Get automation setting value."""
        return self.automation_settings.get(key, default)
    
    def set_automation_setting(self, key: str, value: Any):
        """Set automation setting value."""
        self.automation_settings[key] = value
