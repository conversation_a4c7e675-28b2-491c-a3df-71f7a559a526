from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    SpStreamFormatConverter, DISPID_SRIsShared,
    SpeechPropertyComplexResponseSpeed, SLTApp,
    DISPID_SRSSupportedLanguages, DISPID_SVPriority,
    SECFEmulateResult, SPBO_TIME_UNITS, DISPID_SGRAttributes,
    DISPID_SDKSetLongValue, DISPID_SVSCurrentStreamNumber,
    SAFTCCITT_ALaw_11kHzMono, DISPID_SASetState,
    tagSPTEXTSELECTIONINFO, SVEAudioLevel, DISPID_SWFEBlockAlign,
    DISPID_SABufferInfo, SPAR_Unknown, SPCT_DICTATION,
    DISPID_SVEventInterests, DISPID_SGRsFindRule, SAFT16kHz8BitMono,
    STCInprocServer, eLEXTYPE_USER, SAFTADPCM_44kHzStereo,
    SPSHT_EMAIL, DISPID_SRCERecognizerStateChange, SSTTWildcard,
    DISPID_SPEsCount, SPPS_LMA, SINone, SPWP_KNOWN_WORD_PRONOUNCEABLE,
    DISPID_SRGCmdLoadFromMemory, SWTAdded, SAFTExtendedAudioFormat,
    DISPID_SGRSTNextState, SpeechRegistryUserRoot, SVP_7,
    SPINTERFERENCE_NOISE, SpeechPropertyResponseSpeed, SVP_13,
    SPEI_WORD_BOUNDARY, DISPID_SRDisplayUI,
    DISPID_SPEDisplayAttributes, ISpeechVoiceStatus,
    DISPID_SGRSTs_NewEnum, ISpObjectToken, eLEXTYPE_LETTERTOSOUND,
    DISPID_SRGCmdSetRuleIdState, SINoise, ISpeechLexiconWord,
    eLEXTYPE_PRIVATE8, SVP_19, DISPID_SAFType, DISPID_SRCBookmark,
    SAFTCCITT_ALaw_22kHzMono, SpSharedRecoContext,
    ISpeechWaveFormatEx, SPSHT_OTHER, eLEXTYPE_PRIVATE10,
    DISPID_SRGSetWordSequenceData, DISPID_SRStatus,
    DISPID_SWFEBitsPerSample, SPAS_CLOSED,
    SPRS_ACTIVE_WITH_AUTO_PAUSE, DISPID_SPIElements,
    ISpNotifyTranslator, DISPID_SRGCmdLoadFromProprietaryGrammar,
    SPEI_SR_BOOKMARK, WSTRING, DISPID_SOTsCount,
    DISPID_SRRTOffsetFromStart, SAFTNoAssignedFormat, WAVEFORMATEX,
    SAFT8kHz16BitStereo, ISpeechPhraseProperties, eWORDTYPE_ADDED,
    ISpSerializeState, SREBookmark, DISPID_SLGenerationId,
    SpeechTokenKeyAttributes, typelib_path, SPFM_OPEN_READWRITE,
    ISpMMSysAudio, DISPID_SLPsCount, SAFT22kHz8BitMono,
    DISPID_SRCEInterference, SVP_8, DISPID_SAEventHandle,
    DISPID_SRRRecoContext, ISpeechVoice, SAFTCCITT_uLaw_8kHzMono,
    DISPID_SPIGrammarId, SDA_No_Trailing_Space,
    DISPID_SLGetGenerationChange, DISPID_SVSLastStreamNumberQueued,
    DISPID_SOTCGetDataKey, DISPID_SCSBaseStream,
    DISPID_SRIsUISupported, DISPID_SRGIsPronounceable, SDTReplacement,
    DISPID_SFSOpen, SPRST_ACTIVE, SAFTTrueSpeech_8kHz1BitMono,
    SSFMOpenReadWrite, SpeechPropertyNormalConfidenceThreshold,
    SpeechPropertyLowConfidenceThreshold, ISpeechPhraseInfoBuilder,
    DISPID_SRGSetTextSelection, SPEI_TTS_PRIVATE,
    DISPID_SPPEngineConfidence, DISPID_SPRs_NewEnum, SPPHRASEPROPERTY,
    eLEXTYPE_APP, DISPID_SOTCSetId, SPBO_AHEAD, SPPS_RESERVED4,
    SP_VISEME_0, eLEXTYPE_PRIVATE6, DISPID_SRRTimes,
    ISpeechLexiconPronunciation, SPSMF_UPS, DISPID_SRGCmdLoadFromFile,
    ISpeechPhraseReplacement, SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN,
    DISPID_SRRTLength, DISPID_SOTGetAttribute, SRTStandard,
    DISPID_SGRSAddRuleTransition, SAFTNonStandardFormat, SVP_16,
    DISPID_SRCResume, SPPHRASEELEMENT, DISPID_SPEDisplayText, SVP_12,
    DISPID_SOTCreateInstance, DISPID_SRCSetAdaptationData,
    DISPID_SRRSetTextFeedback, DISPID_SVSkip, SPEI_SR_PRIVATE, SVP_3,
    DISPID_SRCRetainedAudio, SAFT48kHz8BitMono, SAFTGSM610_8kHzMono,
    DISPID_SRRAudio, ISpRecognizer, SVEViseme, SRAExport, SFTSREngine,
    DISPID_SGRSAddSpecialTransition,
    SPINTERFERENCE_LATENCY_TRUNCATE_END, SECNormalConfidence,
    DISPID_SPARecoResult, DISPID_SPPs_NewEnum, SAFT48kHz8BitStereo,
    SAFTADPCM_44kHzMono, DISPID_SPCPhoneToId, SPFM_NUM_MODES,
    SDKLCurrentUser, SPCT_SUB_COMMAND, SPGS_ENABLED,
    Speech_StreamPos_Asap, SPPS_NotOverriden, DISPID_SVEStreamStart,
    SPEI_RESERVED5, SVEPhoneme, SpVoice, SREAudioLevel,
    SPDKL_CurrentUser, DISPID_SDKEnumKeys, SGDisplay, DISPID_SLWsItem,
    DISPID_SOTSetId, DISPID_SVEBookmark, DISPID_SPRDisplayAttributes,
    DISPID_SPRNumberOfElements, SPRECOGNIZERSTATUS, SPEI_PHONEME,
    DISPID_SGRsCount, SGRSTTTextBuffer, SP_VISEME_9,
    DISPID_SOTIsUISupported, SPFM_CREATE, SPBO_NONE,
    SPPHRASEREPLACEMENT, SVSFPurgeBeforeSpeak, SASRun,
    SVESentenceBoundary, DISPID_SABIBufferSize, SPCT_SLEEP,
    DISPID_SAStatus, DISPID_SPRuleChildren, DISPID_SRGCmdSetRuleState,
    SPRS_INACTIVE, SVSFParseSsml, SpeechGrammarTagUnlimitedDictation,
    SP_VISEME_19, dispid, DISPID_SLPPartOfSpeech,
    DISPID_SOTRemoveStorageFileName, DISPID_SRCRetainedAudioFormat,
    SPRECORESULTTIMES, DISPID_SOTMatchesAttributes, SPPS_Interjection,
    DISPID_SVStatus, SPWORD, DISPID_SPRText, SDTAll, SVSFNLPSpeakPunc,
    DISPID_SRSetPropertyString, ISpeechPhraseAlternate,
    SPRST_ACTIVE_ALWAYS, SPWORDLIST, DISPID_SVGetVoices,
    DISPID_SLWWord, SVPNormal, DISPID_SVSyncronousSpeakTimeout,
    SAFT11kHz8BitMono, SpCustomStream, SRTAutopause, SPSNotOverriden,
    SpeechPropertyHighConfidenceThreshold, DISPID_SRCEventInterests,
    DISPID_SVEPhoneme, SAFT16kHz16BitStereo,
    SpeechDictationTopicSpelling, SPEI_MAX_SR, DISPID_SVSPhonemeId,
    DISPID_SVGetAudioOutputs, SPPS_Noncontent, DISPID_SGRId,
    SpeechUserTraining, SPEI_SR_AUDIO_LEVEL,
    ISpeechGrammarRuleStateTransition, SWPUnknownWordUnpronounceable,
    SAFT44kHz16BitMono, STSF_AppData, DISPID_SPRsCount, SLTUser,
    DISPID_SPPName, DISPID_SRRDiscardResultInfo, ISpeechMMSysAudio,
    SpPhoneticAlphabetConverter, DISPID_SPIEngineId, SASStop,
    ISpNotifySource, SGLexical, SAFTCCITT_uLaw_11kHzMono,
    _ISpeechVoiceEvents, eLEXTYPE_PRIVATE11, DISPID_SRCCreateGrammar,
    DISPID_SPAsItem, DISPID_SPEPronunciation, SGSEnabled,
    DISPID_SRAudioInput, DISPID_SGRSTsItem, SpInprocRecognizer,
    DISPID_SOTsItem, DISPID_SGRSTWeight, DISPID_SRCRecognizer,
    ISpeechCustomStream, DISPID_SWFEAvgBytesPerSec,
    SPEI_SENTENCE_BOUNDARY, ISpeechObjectToken, SITooLoud,
    SDTPronunciation, SAFTGSM610_11kHzMono, wireHWND,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet, ISpeechBaseStream,
    SPRS_ACTIVE_USER_DELIMITED, SAFTCCITT_ALaw_8kHzMono,
    SPRECOCONTEXTSTATUS, SAFTGSM610_22kHzMono, DISPID_SBSFormat,
    DISPID_SPEs_NewEnum, SPEI_RECO_STATE_CHANGE, SPBO_PAUSE,
    STSF_CommonAppData, DISPID_SOTCDefault, SGDSInactive,
    SRADefaultToActive, SLODynamic, DISPID_SLRemovePronunciation,
    SPSLMA, SAFTADPCM_22kHzStereo, DISPID_SASState,
    DISPIDSPTSI_SelectionLength, DISPID_SRRAudioFormat,
    SAFT48kHz16BitMono, ISpStream, SDTAlternates, ISpeechFileStream,
    SpeechVoiceSkipTypeSentence, STCAll, SPEVENT,
    DISPID_SVSInputSentenceLength, DISPID_SMSSetData, DISPID_SLPType,
    SAFTCCITT_ALaw_8kHzStereo, DISPID_SAFSetWaveFormatEx,
    DISPID_SGRClear, SpeechAllElements, SPEI_RESERVED3,
    SPBINARYGRAMMAR, SRTReSent, SPEI_VISEME, SAFTDefault,
    SpeechCategoryPhoneConverters, SRESoundEnd,
    ISpeechRecoResultDispatch, SASPause, DISPID_SAFGetWaveFormatEx,
    ULONG_PTR, SVSFDefault, SPAR_Low, SVPAlert, SPSFunction,
    DISPID_SOTDataKey, DISPID_SAVolume, DISPID_SVEWord,
    SpeechCategoryRecognizers, SVSFParseSapi, SPAO_NONE,
    SPGS_EXCLUSIVE, DISPID_SPRuleId, DISPID_SGRsCommit, SPAS_STOP,
    DISPID_SGRSTPropertyName, SP_VISEME_7, SpeechTokenKeyUI,
    DISPID_SGRSTText, ISpeechMemoryStream, eLEXTYPE_PRIVATE17,
    eLEXTYPE_RESERVED8, SPRST_INACTIVE, ISpeechPhraseElement,
    SRSActive, SPEI_MAX_TTS, ISpeechDataKey, _ULARGE_INTEGER,
    SVEStartInputStream, SP_VISEME_8, DISPID_SVAudioOutputStream,
    SGRSTTWord, SVEPrivate, DISPID_SPEEngineConfidence,
    SPWT_LEXICAL_NO_SPECIAL_CHARS, SAFT32kHz8BitMono, SVP_1,
    SVSFParseAutodetect, SVP_18, DISPID_SGRSTType,
    DISPID_SWFESamplesPerSec, SPPHRASERULE, DISPID_SRCESoundStart,
    SpeechEngineProperties, SpeechAudioProperties, SPVOICESTATUS,
    DISPID_SOTGetStorageFileName, SAFTCCITT_ALaw_44kHzStereo,
    SSSPTRelativeToStart, IServiceProvider, SECFNoSpecialChars,
    SPSNoun, DISPID_SRRTStreamTime, SPSModifier, DISPID_SGRsAdd,
    DISPID_SRCEStartStream, ISpShortcut, SAFT11kHz16BitStereo,
    DISPID_SRCESoundEnd, DISPID_SPIAudioSizeTime, SAFT8kHz8BitMono,
    SVP_5, SP_VISEME_11, ISpPhraseAlt, SGRSTTRule, ISpObjectWithToken,
    SECLowConfidence, DISPID_SVGetAudioInputs,
    DISPID_SRSNumberOfActiveRules, DISPID_SGRSTPropertyId, SPPHRASE,
    DISPID_SLPSymbolic, ISpProperties, DISPID_SRCEFalseRecognition,
    SPDKL_DefaultLocation, SAFT16kHz16BitMono, SREFalseRecognition,
    SGDSActiveWithAutoPause, DISPID_SRCCmdMaxAlternates,
    SRERecoOtherContext, DISPID_SRCAudioInInterferenceStatus,
    eLEXTYPE_PRIVATE2, SPAUDIOSTATUS, eLEXTYPE_MORPHOLOGY,
    DISPID_SPPValue, DISPID_SVSInputSentencePosition,
    SAFT24kHz8BitMono, SAFT24kHz16BitMono, ISpPhrase, SGSDisabled,
    DISPID_SGRSTRule, ISpPhoneticAlphabetConverter, ISpDataKey,
    SPINTERFERENCE_NONE, SPPS_Verb, DISPID_SLGetPronunciations,
    ISpeechRecoResultTimes, DISPID_SWFEFormatTag, ISpeechGrammarRules,
    SDTLexicalForm, DISPID_SPPsItem, SDTAudio, DISPID_SAFGuid,
    DISPID_SPEAudioSizeBytes, Speech_Max_Pron_Length, SPSHORTCUTPAIR,
    SpNullPhoneConverter, DISPID_SPIGetText,
    SAFTCCITT_ALaw_22kHzStereo, DISPID_SPEAudioStreamOffset,
    DISPID_SDKSetBinaryValue, DISPID_SDKDeleteKey, SPVPRI_ALERT,
    DISPID_SPIRetainedSizeBytes, SPRST_INACTIVE_WITH_PURGE,
    DISPID_SRGRecoContext, eLEXTYPE_PRIVATE1,
    SPSMF_SRGS_SAPIPROPERTIES, SpeechRegistryLocalMachineRoot,
    SP_VISEME_15, SREStreamEnd, DISPID_SRCEPropertyNumberChange,
    DISPID_SPRFirstElement, SVEEndInputStream,
    DISPID_SPRuleConfidence, SLOStatic,
    DISPID_SLRemovePronunciationByPhoneIds, SVF_Stressed,
    SRCS_Enabled, SRERecognition, SAFTText, DISPID_SGRSTPropertyValue,
    _ISpeechRecoContextEvents, DISPID_SLPs_NewEnum, SAFT12kHz8BitMono,
    DISPID_SRRTTickCount, DISPID_SPRules_NewEnum, SPWF_INPUT,
    SWTDeleted, DISPID_SRCVoicePurgeEvent, _RemotableHandle,
    ISpeechLexicon, SAFT24kHz8BitStereo, SVSFParseMask,
    DISPID_SPPFirstElement, SP_VISEME_6, SVP_21, SP_VISEME_14,
    IInternetSecurityMgrSite, DISPIDSPTSI_ActiveLength,
    SPSERIALIZEDPHRASE, eLEXTYPE_VENDORLEXICON,
    SAFTCCITT_uLaw_8kHzStereo, DISPID_SGRs_NewEnum, SPAUDIOBUFFERINFO,
    SPEI_SR_RETAINEDAUDIO, SP_VISEME_5, SAFT32kHz16BitMono,
    SPSHORTCUTPAIRLIST, tagSPPROPERTYINFO, SPWT_PRONUNCIATION,
    SDTProperty, SPPS_Modifier, SPEI_HYPOTHESIS, SPFM_CREATE_ALWAYS,
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE, SP_VISEME_10,
    SPEI_ACTIVE_CATEGORY_CHANGED, SPEI_RECO_OTHER_CONTEXT,
    SPWORDPRONUNCIATIONLIST, ISpRecoContext, SPINTERFERENCE_TOOLOUD,
    DISPID_SOTRemove, SPAR_Medium, SREPrivate, ISpAudio,
    SPINTERFERENCE_NOSIGNAL, SSFMCreateForWrite,
    ISpObjectTokenCategory, tagSTATSTG, DISPID_SOTId, ISpRecoResult,
    ISpNotifySink, SVSFUnusedFlags, SPAR_High, SRTExtendableParse,
    DISPID_SRRecognizer, SGRSTTWildcard, ISpGrammarBuilder,
    ISpeechPhraseRules, SRESoundStart, SVP_9, DISPID_SWFEExtraData,
    DISPID_SPPBRestorePhraseFromMemory,
    SPINTERFERENCE_LATENCY_WARNING, DISPID_SABIEventBias,
    SPAO_RETAIN_AUDIO, DISPID_SPEActualConfidence,
    SDKLDefaultLocation, DISPID_SRGState, SpAudioFormat,
    SAFT44kHz16BitStereo, IUnknown, DISPID_SPAsCount,
    DISPID_SRCEPhraseStart, DISPID_SLAddPronunciationByPhoneIds,
    eLEXTYPE_PRIVATE15, SP_VISEME_1, SpeechCategoryAudioOut,
    DISPID_SRSCurrentStreamPosition, UINT_PTR,
    DISPID_SDKGetBinaryValue, DISPID_SLWsCount,
    ISpeechAudioBufferInfo, SRSInactive, SDA_One_Trailing_Space,
    SREPropertyNumChange, SREInterference, SpeechAddRemoveWord,
    SP_VISEME_21, DISPID_SPPId, SpeechAudioFormatGUIDText,
    SpPhoneConverter, SGPronounciation, SAFT22kHz16BitStereo,
    SPPS_Noun, DISPID_SRSClsidEngine, DISPID_SPAPhraseInfo,
    __MIDL___MIDL_itf_sapi_0000_0020_0001, SpShortcut,
    SpCompressedLexicon, SPEI_RESERVED1, SPPS_RESERVED1,
    DISPID_SVEVoiceChange, SPEI_RESERVED2, DISPID_SPRsItem,
    SRTSMLTimeout, DISPID_SLWLangId, DISPID_SPRuleName,
    DISPID_SVSLastResult, CoClass, DISPID_SLAddPronunciation,
    DISPID_SRRPhraseInfo, DISPID_SPPNumberOfElements,
    ISpeechAudioStatus, ISpeechGrammarRuleStateTransitions,
    DISPID_SLWs_NewEnum, DISPID_SGRsItem, SAFTADPCM_8kHzStereo,
    SPVPRI_NORMAL, SpUnCompressedLexicon, SAFTCCITT_ALaw_44kHzMono,
    SPSInterjection, DISPID_SVSLastBookmarkId,
    DISPID_SRSetPropertyNumber, DISPID_SPIAudioStreamPosition,
    DISPID_SVRate, DISPID_SRProfile, DISPID_SGRSRule, SVP_0,
    SPEI_PHRASE_START, SAFTCCITT_uLaw_44kHzMono,
    eLEXTYPE_USER_SHORTCUT, STCRemoteServer, DISPID_SVDisplayUI,
    SPWORDPRONUNCIATION, GUID, SDKLCurrentConfig, DISPID_SBSWrite,
    SVP_6, SVEVoiceChange, ISpPhoneConverter, SPPS_Unknown,
    DISPID_SPRuleParent, SDTRule, ISpeechRecoGrammar,
    DISPID_SRCEEndStream, DISPID_SRRGetXMLErrorInfo,
    DISPID_SVSLastBookmark, Speech_StreamPos_RealTime,
    SpeechCategoryAppLexicons, DISPID_SPANumberOfElementsInResult,
    DISPID_SPEsItem, SpObjectToken, SpeechRecoProfileProperties,
    DISPID_SPPsCount, SPEI_START_SR_STREAM, SPEI_RECOGNITION,
    DISPID_SVSpeakCompleteEvent, eLEXTYPE_PRIVATE3, SPPROPERTYINFO,
    SAFT44kHz8BitStereo, SpStream, DISPID_SRGetRecognizers,
    SPEI_SOUND_START, DISPID_SLWType, SECHighConfidence, SPSUnknown,
    DISPID_SGRSAddWordTransition, DISPID_SPIAudioSizeBytes,
    SPEI_VOICE_CHANGE, DISPID_SADefaultFormat,
    SDA_Two_Trailing_Spaces, DISPID_SPERetainedSizeBytes, SVP_14,
    SRTEmulated, SAFTCCITT_uLaw_22kHzMono, SpeechCategoryAudioIn,
    SPEI_TTS_BOOKMARK, ISequentialStream, SPSEMANTICERRORINFO,
    SVSFNLPMask, SPGS_DISABLED, SpeechPropertyResourceUsage,
    SP_VISEME_3, SPEI_END_INPUT_STREAM, DISPID_SVSRunningState,
    HRESULT, SAFT11kHz8BitStereo, ISpRecoCategory,
    SSSPTRelativeToCurrentPosition, DISPID_SRGCmdLoadFromResource,
    DISPID_SLGetWords, DISPID_SVPause, SAFTCCITT_uLaw_44kHzStereo,
    DISPID_SASCurrentDevicePosition, IStream, DISPID_SASNonBlockingIO,
    SRCS_Disabled, SpInProcRecoContext, DISPID_SVEStreamEnd,
    DISPID_SPRulesItem, SVPOver, SPDKL_CurrentConfig, SPVPRI_OVER,
    ISpeechRecoContext, SAFTCCITT_ALaw_11kHzStereo, SBOPause,
    SPRST_NUM_STATES, eLEXTYPE_PRIVATE13, ISpResourceManager,
    DISPID_SVSpeakStream, SPCT_COMMAND, ISpRecognizer2,
    SpTextSelectionInformation, ISpeechPhoneConverter,
    eLEXTYPE_PRIVATE7, DISPID_SRCEAdaptation, SPSVerb,
    DISPID_SMSALineId, DISPID_SRCERecognition, DISPID_SPCIdToPhone,
    SP_VISEME_2, SRARoot, SVEWordBoundary, SAFTCCITT_uLaw_11kHzStereo,
    SAFT22kHz16BitMono, SpMemoryStream, SPCS_DISABLED,
    SPSERIALIZEDRESULT, SFTInput, SVSFIsXML, DISPID_SRCState,
    SITooFast, DISPID_SPIGetDisplayAttributes, SVP_4,
    DISPID_SREmulateRecognition, DISPID_SVSVisemeId,
    SPINTERFERENCE_TOOSLOW, ISpeechPhraseProperty,
    ISpeechPhraseAlternates, SVEBookmark, ISpeechRecognizer, LONG_PTR,
    SREPhraseStart, SREStateChange, DISPID_SABufferNotifySize,
    ISpeechAudioFormat, SAFT24kHz16BitStereo, SPEI_SOUND_END,
    SAFT16kHz8BitStereo, ISpRecoContext2, SP_VISEME_12, SRSEDone,
    DISPID_SRAudioInputStream, ISpeechObjectTokens, SPEI_ADAPTATION,
    SpeechAudioVolume, SPPS_SuppressWord, DISPID_SPELexicalForm,
    DISPID_SVIsUISupported, DISPID_SFSClose,
    DISPID_SRCERecognitionForOtherContext, DISPID_SPAs_NewEnum,
    DISPID_SGRsDynamic, SpPhraseInfoBuilder, SPPS_RESERVED3,
    SPAS_PAUSE, SSTTDictation, DISPID_SMSGetData,
    DISPID_SPRuleNumberOfElements, SDKLLocalMachine,
    SpResourceManager, DISPID_SVAlertBoundary, DISPID_SVEViseme,
    DISPMETHOD, DISPID_SRRAlternates, _check_version,
    ISpeechGrammarRule, DISPID_SLPPhoneIds, DISPID_SRCEEnginePrivate,
    SREAllEvents, ISpeechGrammarRuleState,
    DISPID_SPERequiredConfidence, SAFT32kHz16BitStereo,
    SpeechTokenKeyFiles, SAFTADPCM_11kHzMono, SVSFIsNotXML,
    DISPID_SDKDeleteValue, SPWF_SRENGINE, SBONone, SPEVENTSOURCEINFO,
    DISPID_SVWaitUntilDone, DISPID_SPPConfidence, SpNotifyTranslator,
    SPFM_OPEN_READONLY, DISPID_SRAllowVoiceFormatMatchingOnNextSet,
    SP_VISEME_18, ISpRecognizer3, SVF_Emphasis, ISpeechRecoResult2,
    ISpeechResourceLoader, _FILETIME, SGSExclusive,
    SAFT48kHz16BitStereo, DISPID_SRCERequestUI, eLEXTYPE_RESERVED6,
    DISPID_SGRsCommitAndSave, DISPID_SRGRules, SPEI_RESERVED6,
    SRAORetainAudio, DISPID_SDKEnumValues, DISPID_SRCRequestedUIType,
    DISPID_SRRSaveToMemory, SPWT_DISPLAY, DISPID_SVVolume,
    IInternetSecurityManager, SECFDefault, _LARGE_INTEGER,
    eLEXTYPE_PRIVATE4, DISPID_SPPParent, STSF_FlagCreate,
    ISpStreamFormatConverter, DISPID_SLWPronunciations, SVP_20,
    eLEXTYPE_PRIVATE16, DISPID_SRCVoice, SPEI_END_SR_STREAM,
    ISpeechPhraseInfo, ISpVoice, DISPID_SDKSetStringValue,
    ISpeechTextSelectionInformation, SAFT22kHz8BitStereo,
    SGLexicalNoSpecialChars, eLEXTYPE_PRIVATE20, SpLexicon,
    SPEI_START_INPUT_STREAM, DISPID_SPEAudioSizeTime,
    DISPID_SPERetainedStreamOffset, IEnumSpObjectTokens,
    SECFIgnoreCase, DISPID_SOTGetDescription, ISpXMLRecoResult,
    SRAInterpreter, SDTDisplayText, SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE,
    SRAImport, SpSharedRecognizer, DISPID_SPIEnginePrivateData,
    SPWT_LEXICAL, DISPID_SRCEPropertyStringChange, SP_VISEME_16,
    SPCS_ENABLED, SPPS_RESERVED2, SVP_17, SAFTCCITT_uLaw_22kHzStereo,
    SPSHT_Unknown, helpstring, SVP_2, DISPID_SVEAudioLevel,
    SpeechGrammarTagWildcard, SPEI_PROPERTY_STRING_CHANGE,
    ISpStreamFormat, DISPID_SPIProperties, DISPID_SDKCreateKey,
    DISPID_SPRulesCount, SREPropertyStringChange, SPDKL_LocalMachine,
    SAFTGSM610_44kHzMono, DISPID_SRGReset,
    DISPID_SPRuleEngineConfidence, DISPIDSPTSI_ActiveOffset,
    DISPID_SVAudioOutput, VARIANT_BOOL, BSTR, Speech_Max_Word_Length,
    SPEI_PROPERTY_NUM_CHANGE, DISPID_SGRAddState, SAFT44kHz8BitMono,
    DISPID_SRCPause, DISPID_SRGCommit, SGRSTTEpsilon, SPEI_MIN_SR,
    DISPID_SOTs_NewEnum, SAFTADPCM_8kHzMono, SREStreamStart,
    SPEI_FALSE_RECOGNITION, eLEXTYPE_PRIVATE12, SPSSuppressWord,
    DISPID_SVVoice, SPTEXTSELECTIONINFO, SpMMAudioIn, DISPID_SRGId,
    DISPID_SRGetFormat, SAFT12kHz16BitMono, SREAdaptation,
    eLEXTYPE_RESERVED9, ISpeechObjectTokenCategory, SpeechMicTraining,
    DISPID_SLPLangId, ISpeechAudio, DISPID_SRCEHypothesis, SPXRO_SML,
    DISPID_SRAllowAudioInputFormatChangesOnNextSet,
    DISPID_SRCEBookmark, SRADynamic, DISPID_SASCurrentSeekPosition,
    DISPID_SVGetProfiles, SAFT8kHz16BitMono,
    __MIDL___MIDL_itf_sapi_0000_0020_0002, SPINTERFERENCE_TOOFAST,
    eLEXTYPE_PRIVATE19, SSTTTextBuffer, SP_VISEME_4,
    DISPID_SRGDictationLoad, SAFT12kHz8BitStereo,
    DISPID_SVESentenceBoundary, ISpeechLexiconWords,
    SAFT11kHz16BitMono, DISPID_SRCEAudioLevel, STCInprocHandler,
    ISpRecoGrammar, SAFTADPCM_11kHzStereo,
    DISPID_SRCCreateResultFromMemory, DISPID_SPEAudioTimeOffset,
    Speech_Default_Weight, SGRSTTDictation, SSFMCreate,
    DISPID_SMSAMMHandle, SGDSActive, DISPID_SPISaveToMemory,
    DISPIDSPTSI_SelectionOffset, DISPID_SRGetPropertyString,
    DISPID_SDKGetStringValue, DISPID_SVSInputWordPosition,
    SVSFPersistXML, DISPID_SGRAddResource, DISPID_SRRSpeakAudio,
    DISPID_SRGDictationSetState, SPEI_REQUEST_UI, SPEI_UNDEFINED,
    STSF_LocalAppData, DISPID_SRGCmdLoadFromObject, SpWaveFormatEx,
    SpMMAudioOut, ISpeechPhraseReplacements, SVSFIsFilename,
    ISpeechPhraseElements, DISPID_SWFEChannels,
    DISPID_SPIReplacements, DISPID_SPCLangId, DISPID_SDKOpenKey,
    SVP_11, Library, DISPID_SGRSTsCount, SRATopLevel,
    DISPID_SDKGetlongValue, DISPID_SOTCId, DISPID_SPACommit,
    DISPID_SVSpeak, SPSMF_SRGS_SEMANTICINTERPRETATION_MS,
    SpeechCategoryRecoProfiles, SpFileStream,
    DISPID_SRGetPropertyNumber, eLEXTYPE_PRIVATE14,
    DISPID_SOTCEnumerateTokens, DISPID_SVEEnginePrivate,
    SREHypothesis, ISpeechRecognizerStatus,
    SDA_Consume_Leading_Spaces, SpMMAudioEnum, DISPID_SRState,
    eLEXTYPE_PRIVATE9, ISpeechXMLRecoResult, eLEXTYPE_RESERVED10,
    DISPID_SMSADeviceId, ISpeechPhraseRule,
    DISPID_SABIMinNotification, DISPID_SRGDictationUnload,
    SAFT8kHz8BitStereo, DISPID_SRCreateRecoContext, ISpLexicon,
    DISPID_SPRuleFirstElement, __MIDL_IWinTypes_0009, ISpRecoGrammar2,
    SP_VISEME_20, SVP_15, eLEXTYPE_PRIVATE5,
    ISpeechLexiconPronunciations, SITooSlow, eLEXTYPE_PRIVATE18,
    DISPID_SRSCurrentStreamNumber, DISPID_SPAStartElementInResult,
    DISPID_SGRInitialState, SAFT32kHz8BitStereo, DISPID_SPIRule,
    SRSInactiveWithPurge, SECFIgnoreKanaType, SpeechCategoryVoices,
    DISPID_SVSInputWordLength, eLEXTYPE_RESERVED4, SRSEIsSpeaking,
    SVP_10, STCLocalServer, ISpeechRecoResult, DISPID_SBSSeek,
    COMMETHOD, SPINTERFERENCE_TOOQUIET, SECFIgnoreWidth, VARIANT,
    SWPUnknownWordPronounceable, SAFT12kHz16BitStereo, SPEI_MIN_TTS,
    DISPID_SGRName, DISPID_SOTCategory, SPSMF_SAPI_PROPERTIES,
    SPAS_RUN, SVEAllEvents, DISPID_SOTDisplayUI, SVSFVoiceMask,
    ISpEventSink, DISPID_SASFreeBufferSpace, IEnumString, SITooQuiet,
    SPRS_ACTIVE, DISPID_SRSAudioStatus, SPXRO_Alternates_SML,
    SpeechVoiceCategoryTTSRate, ISpEventSource, SPPS_Function,
    SGDSActiveUserDelimited, SSSPTRelativeToEnd,
    ISpPhoneticAlphabetSelection, DISPID_SPIStartTime,
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C, SPEI_TTS_AUDIO_LEVEL,
    SPLO_DYNAMIC, SPCT_SUB_DICTATION, DISPID_SBSRead,
    SAFTADPCM_22kHzMono, SpObjectTokenCategory,
    SpeechTokenIdUserLexicon, eWORDTYPE_DELETED, SSFMOpenForRead,
    SP_VISEME_17, SPEI_INTERFERENCE, SpeechGrammarTagDictation, _lcid,
    DISPID_SLPsItem, DISPID_SRRGetXMLResult, SPRULE, SRAONone,
    SVSFlagsAsync, SPLO_STATIC, SVF_None, SRSActiveAlways,
    SPSHT_NotOverriden, eLEXTYPE_RESERVED7, SpeechAudioFormatGUIDWave,
    SINoSignal, SRERequestUI, SpeechTokenValueCLSID, SP_VISEME_13,
    DISPID_SVResume, DISPID_SPILanguageId, SASClosed,
    DISPID_SGRSTransitions, DISPID_SPPChildren,
    SpeechPropertyAdaptationOn, SWPKnownWordPronounceable
)


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


SPAUDIOSTATE = _SPAUDIOSTATE
SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE


__all__ = [
    'SP_VISEME_1', 'SpeechCategoryAudioOut',
    'SpStreamFormatConverter', 'DISPID_SRIsShared',
    'SpeechPropertyComplexResponseSpeed',
    'DISPID_SRSCurrentStreamPosition', 'SLTApp',
    'DISPID_SRSSupportedLanguages', 'UINT_PTR',
    'DISPID_SDKGetBinaryValue', 'DISPID_SVPriority',
    'DISPID_SLWsCount', 'ISpeechAudioBufferInfo',
    'DISPID_SpeechCustomStream', 'SRSInactive',
    'SDA_One_Trailing_Space', 'SREPropertyNumChange',
    'SPSEMANTICFORMAT', 'SECFEmulateResult', 'SREInterference',
    'SPBO_TIME_UNITS', 'DISPID_SGRAttributes',
    'DISPID_SDKSetLongValue', 'DISPID_SVSCurrentStreamNumber',
    'SAFTCCITT_ALaw_11kHzMono', 'DISPID_SASetState',
    'SpeechAddRemoveWord', 'tagSPTEXTSELECTIONINFO', 'SVEAudioLevel',
    'DISPID_SWFEBlockAlign', 'DISPID_SABufferInfo', 'SP_VISEME_21',
    'DISPID_SPPId', 'SPAR_Unknown', 'SPVISEMES', 'SPCT_DICTATION',
    'SpeechAudioFormatGUIDText', 'SpPhoneConverter',
    'DISPID_SVEventInterests', 'SAFT22kHz16BitStereo',
    'SAFT16kHz8BitMono', 'DISPID_SGRsFindRule', 'STCInprocServer',
    'SGPronounciation', 'SPPS_Noun', 'eLEXTYPE_USER',
    'DISPID_SRSClsidEngine', 'DISPID_SPAPhraseInfo',
    '__MIDL___MIDL_itf_sapi_0000_0020_0001', 'SpShortcut',
    'SpeechVisemeFeature', 'SpCompressedLexicon',
    'SAFTADPCM_44kHzStereo', 'SPEI_RESERVED1', 'SPPARTOFSPEECH',
    'SPSHT_EMAIL', 'SPPS_RESERVED1', 'SPRECOSTATE', 'SSTTWildcard',
    'SPPS_LMA', 'SINone', 'SpeechEmulationCompareFlags',
    'DISPID_SRCERecognizerStateChange', 'DISPID_SPEsCount',
    'SPWP_KNOWN_WORD_PRONOUNCEABLE', 'DISPID_SRGCmdLoadFromMemory',
    'SWTAdded', 'DISPID_SVEVoiceChange', 'SPEI_RESERVED2',
    'DISPID_SPRsItem', 'SRTSMLTimeout', 'SAFTExtendedAudioFormat',
    'DISPID_SpeechVoice', 'DISPID_SGRSTNextState',
    'SpeechRegistryUserRoot', 'SVP_7', 'DISPID_SLWLangId',
    'DISPID_SPRuleName', 'SPINTERFERENCE_NOISE',
    'DISPID_SVSLastResult', 'SPGRAMMARSTATE',
    'SpeechPropertyResponseSpeed', 'DISPID_SLAddPronunciation',
    'SVP_13', 'DISPID_SRRPhraseInfo', 'SPEI_WORD_BOUNDARY',
    'DISPID_SPPNumberOfElements', 'DISPID_SRDisplayUI',
    'DISPID_SPEDisplayAttributes',
    'ISpeechGrammarRuleStateTransitions', 'DISPID_SpeechPhraseRule',
    'DISPID_SLWs_NewEnum', 'ISpeechVoiceStatus',
    'SpeechRecognizerState', 'DISPID_SGRSTs_NewEnum',
    'DISPID_SGRsItem', 'SAFTADPCM_8kHzStereo', 'SPVPRI_NORMAL',
    'ISpObjectToken', 'SpUnCompressedLexicon',
    'SAFTCCITT_ALaw_44kHzMono', 'eLEXTYPE_LETTERTOSOUND',
    'SPSInterjection', 'DISPID_SVSLastBookmarkId',
    'DISPID_SRSetPropertyNumber', 'DISPID_SRGCmdSetRuleIdState',
    'SINoise', 'DISPID_SPIAudioStreamPosition', 'ISpeechLexiconWord',
    'eLEXTYPE_PRIVATE8', 'SVP_19', 'DISPID_SAFType', 'DISPID_SVRate',
    'SVP_0', 'DISPID_SRProfile', 'SPEI_PHRASE_START',
    'SAFTCCITT_uLaw_44kHzMono', 'DISPID_SRCBookmark',
    'DISPID_SGRSRule', 'SPCONTEXTSTATE', 'eLEXTYPE_USER_SHORTCUT',
    'STCRemoteServer', 'SAFTCCITT_ALaw_22kHzMono', 'SPAUDIOSTATE',
    'SPWORDPRONUNCIATION', 'DISPID_SVDisplayUI',
    'SpSharedRecoContext', 'ISpeechWaveFormatEx', 'SPSHT_OTHER',
    'eLEXTYPE_PRIVATE10', 'DISPID_SRGSetWordSequenceData',
    'SDKLCurrentConfig', 'DISPID_SBSWrite', 'SVP_6', 'SVEVoiceChange',
    'SPWORDTYPE', 'ISpPhoneConverter', 'DISPID_SWFEBitsPerSample',
    'SPAS_CLOSED', 'DISPID_SRStatus', 'SpeechRunState',
    'SPRS_ACTIVE_WITH_AUTO_PAUSE', 'DISPID_SPIElements',
    'ISpNotifyTranslator', 'SPPS_Unknown',
    'DISPID_SRGCmdLoadFromProprietaryGrammar', 'SPEI_SR_BOOKMARK',
    'DISPID_SPRuleParent', 'DISPID_SOTsCount',
    'DISPID_SRRTOffsetFromStart', 'SAFTNoAssignedFormat',
    'WAVEFORMATEX', 'SAFT8kHz16BitStereo', 'ISpeechPhraseProperties',
    'SPCATEGORYTYPE', 'eWORDTYPE_ADDED', 'SpeechDisplayAttributes',
    'DISPID_SpeechRecognizerStatus', 'ISpSerializeState', 'SDTRule',
    'SREBookmark', 'DISPID_SLGenerationId',
    'SpeechTokenKeyAttributes', 'ISpeechRecoGrammar', 'typelib_path',
    'SPFM_OPEN_READWRITE', 'ISpMMSysAudio', 'DISPID_SRCEEndStream',
    'DISPID_SRRGetXMLErrorInfo', 'DISPID_SLPsCount',
    'DISPID_SVSLastBookmark', 'Speech_StreamPos_RealTime',
    'SpeechCategoryAppLexicons', 'SAFT22kHz8BitMono',
    'DISPID_SPANumberOfElementsInResult',
    'DISPID_SpeechPhraseBuilder', 'DISPID_SRCEInterference', 'SVP_8',
    'DISPID_SPEsItem', 'SpObjectToken', 'DISPID_SAEventHandle',
    'SpeechRecoProfileProperties', 'DISPID_SRRRecoContext',
    'ISpeechVoice', 'SAFTCCITT_uLaw_8kHzMono', 'DISPID_SPIGrammarId',
    'DISPID_SPPsCount', 'SDA_No_Trailing_Space',
    'DISPID_SLGetGenerationChange', 'SPEI_START_SR_STREAM',
    'SPEI_RECOGNITION', 'DISPID_SVSpeakCompleteEvent',
    'eLEXTYPE_PRIVATE3', 'DISPID_SOTCGetDataKey',
    'DISPID_SCSBaseStream', 'DISPID_SVSLastStreamNumberQueued',
    'DISPID_SpeechPhraseElement', 'SPPROPERTYINFO',
    'DISPID_SRIsUISupported', 'SDTReplacement',
    'DISPID_SRGIsPronounceable', 'SAFT44kHz8BitStereo',
    'DISPID_SFSOpen', 'SPRST_ACTIVE', 'SAFTTrueSpeech_8kHz1BitMono',
    'SPEI_SOUND_START', 'SpStream', 'SSFMOpenReadWrite',
    'SpeechPropertyNormalConfidenceThreshold',
    'SpeechPropertyLowConfidenceThreshold', 'DISPID_SRGetRecognizers',
    'DISPID_SLWType', 'ISpeechPhraseInfoBuilder', 'SECHighConfidence',
    'DISPID_SRGSetTextSelection', 'SPEI_TTS_PRIVATE',
    'DISPID_SPPEngineConfidence', 'SPSUnknown',
    'DISPID_SGRSAddWordTransition', 'DISPID_SPIAudioSizeBytes',
    'DISPID_SPRs_NewEnum', 'SPPHRASEPROPERTY', 'eLEXTYPE_APP',
    'DISPID_SOTCSetId', 'SPEI_VOICE_CHANGE', 'DISPID_SADefaultFormat',
    'SPBO_AHEAD', 'SDA_Two_Trailing_Spaces', 'SPPS_RESERVED4',
    'SVP_14', 'DISPID_SpeechVoiceEvent', 'SRTEmulated',
    'eLEXTYPE_PRIVATE6', 'DISPID_SRRTimes',
    'DISPID_SPERetainedSizeBytes', 'SP_VISEME_0',
    'SAFTCCITT_uLaw_22kHzMono', 'ISpeechLexiconPronunciation',
    'SPSMF_UPS', 'DISPID_SRGCmdLoadFromFile', 'SpeechCategoryAudioIn',
    'ISpeechPhraseReplacement', 'SpeechStreamFileMode',
    'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN',
    'DISPID_SpeechAudioStatus', 'SPEI_TTS_BOOKMARK',
    'SpeechAudioState', 'DISPID_SRRTLength', 'SPSEMANTICERRORINFO',
    'DISPID_SpeechPhraseAlternates', 'SVSFNLPMask', 'SPGS_DISABLED',
    'SRTStandard', 'DISPID_SOTGetAttribute',
    'SpeechPropertyResourceUsage', 'SP_VISEME_3',
    'DISPID_SGRSAddRuleTransition', 'SPEI_END_INPUT_STREAM',
    'SAFTNonStandardFormat', 'DISPID_SVSRunningState',
    'SAFT11kHz8BitStereo', 'SVP_16', 'DISPID_SRCResume',
    'SPPHRASEELEMENT', 'DISPID_SpeechGrammarRules',
    'DISPID_SPEDisplayText', 'SPFILEMODE', 'ISpRecoCategory',
    'SVP_12', 'SSSPTRelativeToCurrentPosition',
    'DISPID_SOTCreateInstance', 'DISPID_SRCSetAdaptationData',
    'DISPID_SRRSetTextFeedback', 'DISPID_SVSkip',
    'SPSMF_SAPI_PROPERTIES', 'SPEI_SR_PRIVATE',
    'DISPID_SRGCmdLoadFromResource', 'SVP_3', 'DISPID_SVPause',
    'DISPID_SLGetWords', 'SAFTCCITT_uLaw_44kHzStereo',
    'DISPID_SASCurrentDevicePosition', 'IStream',
    'DISPID_SRCRetainedAudio', 'DISPID_SpeechLexicon',
    'SAFT48kHz8BitMono', 'SAFTGSM610_8kHzMono', 'SRCS_Disabled',
    'SpeechPartOfSpeech', 'DISPID_SASNonBlockingIO',
    'DISPID_SRRAudio', 'ISpRecognizer', 'SpInProcRecoContext',
    'DISPID_SVEStreamEnd', 'SVEViseme', 'DISPID_SPRulesItem',
    'SVPOver', 'SPDKL_CurrentConfig', 'SRAExport', 'SpeechLoadOption',
    'SFTSREngine', 'DISPID_SGRSAddSpecialTransition',
    'DISPID_SpeechGrammarRuleStateTransitions',
    'SPINTERFERENCE_LATENCY_TRUNCATE_END', 'SECNormalConfidence',
    'DISPID_SPARecoResult', 'DISPID_SPPs_NewEnum',
    'SAFT48kHz8BitStereo', 'SAFTADPCM_44kHzMono',
    'DISPID_SPCPhoneToId', 'SPFM_NUM_MODES', 'SDKLCurrentUser',
    'SPCT_SUB_COMMAND', 'SPVPRI_OVER', 'ISpeechRecoContext',
    'SPGS_ENABLED', 'SAFTCCITT_ALaw_11kHzStereo',
    'Speech_StreamPos_Asap', 'SPPS_NotOverriden', 'SBOPause',
    'SPRST_NUM_STATES', 'DISPID_SVEStreamStart', 'SPEI_RESERVED5',
    'eLEXTYPE_PRIVATE13', 'ISpResourceManager',
    'DISPID_SVSpeakStream', 'DISPID_SpeechAudioFormat', 'SVEPhoneme',
    'SpVoice', 'SPCT_COMMAND', 'ISpRecognizer2',
    'SpTextSelectionInformation', 'ISpeechPhoneConverter',
    'SREAudioLevel', 'eLEXTYPE_PRIVATE7', 'DISPID_SRCEAdaptation',
    'SPSVerb', 'SPDKL_CurrentUser', 'DISPID_SDKEnumKeys',
    'DISPID_SMSALineId', 'DISPID_SRCERecognition', 'SGDisplay',
    'DISPID_SPCIdToPhone', 'SRARoot', 'SVEWordBoundary',
    'SpeechSpecialTransitionType', 'SP_VISEME_2',
    'SAFTCCITT_uLaw_11kHzStereo', 'DISPID_SLWsItem', 'SPEVENTENUM',
    'DISPID_SOTSetId', 'DISPID_SVEBookmark', 'SAFT22kHz16BitMono',
    'DISPID_SPRDisplayAttributes', 'DISPID_SPRNumberOfElements',
    'SpMemoryStream', 'SPRECOGNIZERSTATUS', 'SPEI_PHONEME',
    'DISPID_SGRsCount', 'SGRSTTTextBuffer', 'SPCS_DISABLED',
    'DISPID_SOTIsUISupported', 'SP_VISEME_9', 'SPSERIALIZEDRESULT',
    'SFTInput', 'SPFM_CREATE', 'SVSFIsXML', 'DISPID_SRCState',
    'SPBO_NONE', 'SPPHRASEREPLACEMENT', 'SpeechWordType', 'SITooFast',
    'SVSFPurgeBeforeSpeak', 'DISPID_SpeechGrammarRuleState',
    'DISPID_SPIGetDisplayAttributes', 'SVP_4',
    'DISPID_SREmulateRecognition', 'SASRun', 'SVESentenceBoundary',
    'DISPID_SVSVisemeId', 'SPINTERFERENCE_TOOSLOW',
    'ISpeechPhraseProperty', 'SPWAVEFORMATTYPE',
    'DISPID_SABIBufferSize', 'SPVPRIORITY', 'SPCT_SLEEP',
    'DISPID_SAStatus', 'DISPID_SPRuleChildren',
    'DISPID_SRGCmdSetRuleState', 'SPRS_INACTIVE',
    'DISPID_SpeechWaveFormatEx', 'ISpeechPhraseAlternates',
    'SVSFParseSsml', 'SVEBookmark',
    'SpeechGrammarTagUnlimitedDictation', 'ISpeechRecognizer',
    'SP_VISEME_19', 'LONG_PTR', 'DISPID_SLPPartOfSpeech',
    'SREPhraseStart', 'SREStateChange', 'DISPID_SABufferNotifySize',
    'DISPID_SOTRemoveStorageFileName',
    'DISPID_SRCRetainedAudioFormat', 'SPRECORESULTTIMES',
    'ISpeechAudioFormat', 'DISPID_SOTMatchesAttributes',
    'SPPS_Interjection', 'SAFT24kHz16BitStereo', 'SPEI_SOUND_END',
    'SpeechRetainedAudioOptions', 'SpeechEngineConfidence', 'SPWORD',
    'DISPID_SVStatus', 'SDTAll', 'DISPID_SPRText',
    'SAFT16kHz8BitStereo', 'ISpRecoContext2',
    'DISPID_SpeechLexiconProns', 'SVSFNLPSpeakPunc',
    'DISPID_SRSetPropertyString', 'ISpeechPhraseAlternate',
    'DISPID_SpeechBaseStream', 'SPRST_ACTIVE_ALWAYS', 'SPWORDLIST',
    'DISPID_SVGetVoices', 'SP_VISEME_12', 'SRSEDone',
    'DISPID_SRAudioInputStream', 'ISpeechObjectTokens',
    'SPEI_ADAPTATION', 'SVPNormal', 'SPPS_SuppressWord',
    'SpeechAudioVolume', 'DISPID_SVSyncronousSpeakTimeout',
    'SAFT11kHz8BitMono', 'DISPID_SPELexicalForm', 'DISPID_SLWWord',
    'SpCustomStream', 'SRTAutopause', 'SPSNotOverriden',
    'DISPID_SVIsUISupported', 'SpeechPropertyHighConfidenceThreshold',
    'DISPID_SRCEventInterests', 'DISPID_SFSClose',
    'DISPID_SRCERecognitionForOtherContext',
    'DISPID_SpeechMMSysAudio', 'DISPID_SVEPhoneme',
    'DISPID_SPAs_NewEnum', 'DISPID_SGRsDynamic', 'SPRULESTATE',
    'SAFT16kHz16BitStereo', 'SpeechDictationTopicSpelling',
    'SPEI_MAX_SR', 'SpPhraseInfoBuilder', 'SPPS_RESERVED3',
    'SPAS_PAUSE', 'SSTTDictation', 'DISPID_SVSPhonemeId',
    'DISPID_SMSGetData', 'DISPID_SVGetAudioOutputs',
    'DISPID_SPRuleNumberOfElements', 'SPPS_Noncontent',
    'SDKLLocalMachine', 'DISPID_SGRId', 'SpResourceManager',
    'SPEI_SR_AUDIO_LEVEL', 'SpeechUserTraining',
    'SWPUnknownWordUnpronounceable',
    'ISpeechGrammarRuleStateTransition', 'SAFT44kHz16BitMono',
    'DISPID_SVAlertBoundary', 'DISPID_SVEViseme', 'STSF_AppData',
    'DISPID_SPRsCount', 'SLTUser', 'DISPID_SpeechPhraseProperty',
    'DISPID_SRRAlternates', 'DISPID_SPPName', 'ISpeechGrammarRule',
    'DISPID_SLPPhoneIds', 'DISPID_SRCEEnginePrivate', 'SREAllEvents',
    'DISPID_SRRDiscardResultInfo', 'ISpeechGrammarRuleState',
    'SpPhoneticAlphabetConverter', 'SAFT32kHz16BitStereo',
    'DISPID_SPERequiredConfidence', 'ISpeechMMSysAudio',
    'DISPID_SPIEngineId', 'SpeechTokenKeyFiles',
    'SAFTADPCM_11kHzMono', 'SASStop', 'SVSFIsNotXML',
    'ISpNotifySource', 'SPGRAMMARWORDTYPE', 'DISPID_SDKDeleteValue',
    'DISPID_SpeechPhraseElements', 'SPWF_SRENGINE', 'SBONone',
    'SPEVENTSOURCEINFO', 'DISPID_SVWaitUntilDone',
    'DISPID_SpeechRecognizer', 'DISPID_SpeechRecoContext',
    'DISPID_SpeechRecoResult2', 'SGLexical', 'SpNotifyTranslator',
    'SAFTCCITT_uLaw_11kHzMono', 'DISPID_SPPConfidence',
    '_ISpeechVoiceEvents', 'SPFM_OPEN_READONLY', 'SpeechLexiconType',
    'DISPID_SRAllowVoiceFormatMatchingOnNextSet',
    'eLEXTYPE_PRIVATE11', 'DISPID_SRCCreateGrammar',
    'DISPID_SPAsItem', 'SP_VISEME_18', 'DISPID_SPEPronunciation',
    'ISpRecognizer3', 'SGSEnabled', 'SVF_Emphasis',
    'DISPID_SpeechObjectTokens', 'DISPID_SRAudioInput',
    'DISPID_SGRSTsItem', 'SpInprocRecognizer', 'DISPID_SOTsItem',
    'ISpeechResourceLoader', 'ISpeechRecoResult2', 'SGSExclusive',
    'SAFT48kHz16BitStereo', 'SPWORDPRONOUNCEABLE',
    'DISPID_SRCRecognizer', 'DISPID_SRCERequestUI',
    'DISPID_SGRSTWeight', 'eLEXTYPE_RESERVED6',
    'DISPID_SGRsCommitAndSave', 'ISpeechCustomStream',
    'DISPID_SWFEAvgBytesPerSec', 'SPEI_SENTENCE_BOUNDARY',
    'SPEI_RESERVED6', 'SRAORetainAudio', 'DISPID_SDKEnumValues',
    'DISPID_SRGRules', 'ISpeechObjectToken', 'SITooLoud',
    'DISPID_SRCRequestedUIType', '_SPAUDIOSTATE', 'SDTPronunciation',
    'SAFTGSM610_11kHzMono', 'DISPID_SRRSaveToMemory',
    'SPINTERFERENCE', 'SPWT_DISPLAY', 'DISPID_SVVolume',
    'IInternetSecurityManager', 'SECFDefault',
    'DISPID_SVAllowAudioOuputFormatChangesOnNextSet',
    'ISpeechBaseStream', 'SPRS_ACTIVE_USER_DELIMITED',
    'eLEXTYPE_PRIVATE4', 'SAFTCCITT_ALaw_8kHzMono',
    'DISPID_SPPParent', 'STSF_FlagCreate', 'ISpStreamFormatConverter',
    'SPRECOCONTEXTSTATUS', 'DISPID_SLWPronunciations', 'SVP_20',
    'eLEXTYPE_PRIVATE16', 'DISPID_SRCVoice', 'SPEI_END_SR_STREAM',
    'ISpeechPhraseInfo', 'ISpVoice', 'DISPID_SpeechFileStream',
    'DISPID_SDKSetStringValue', 'SAFTGSM610_22kHzMono',
    'DISPID_SBSFormat', 'DISPID_SPEs_NewEnum',
    'SPEI_RECO_STATE_CHANGE', 'SPBO_PAUSE',
    'ISpeechTextSelectionInformation', 'STSF_CommonAppData',
    'SAFT22kHz8BitStereo', 'SGLexicalNoSpecialChars',
    'eLEXTYPE_PRIVATE20', 'SpeechStreamSeekPositionType', 'SpLexicon',
    'DISPID_SOTCDefault', 'SGDSInactive', 'SPEI_START_INPUT_STREAM',
    'DISPID_SPEAudioSizeTime', 'SRADefaultToActive',
    'DISPID_SPERetainedStreamOffset', 'SLODynamic',
    'DISPID_SLRemovePronunciation', 'IEnumSpObjectTokens',
    'SECFIgnoreCase', 'SPSLMA', 'SAFTADPCM_22kHzStereo',
    'DISPID_SOTGetDescription', 'ISpXMLRecoResult', 'SRAInterpreter',
    'SDTDisplayText', 'SAFT48kHz16BitMono',
    'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE', 'DISPID_SASState',
    'ISpStream', 'SDTAlternates', 'SRAImport', 'ISpeechFileStream',
    'SPBOOKMARKOPTIONS', 'SpSharedRecognizer',
    'DISPID_SPIEnginePrivateData', 'SPWT_LEXICAL',
    'SpeechVoiceSkipTypeSentence', 'SPLOADOPTIONS',
    'DISPID_SRCEPropertyStringChange', 'SP_VISEME_16', 'SPCS_ENABLED',
    'SPPS_RESERVED2', 'STCAll', 'SVP_17',
    'DISPID_SpeechPhraseReplacement', 'SPEVENT',
    'SAFTCCITT_uLaw_22kHzStereo', 'SPSHT_Unknown',
    'SpeechRuleAttributes', 'SVP_2', 'DISPID_SVEAudioLevel',
    'DISPID_SVSInputSentenceLength', 'SpeechGrammarTagWildcard',
    'DISPID_SMSSetData', 'DISPID_SLPType',
    'SPEI_PROPERTY_STRING_CHANGE', 'ISpStreamFormat',
    'SAFTCCITT_ALaw_8kHzStereo', 'DISPID_SAFSetWaveFormatEx',
    'DISPID_SGRClear', 'SpeechAllElements', 'DISPID_SPIProperties',
    'SPEI_RESERVED3', 'SPBINARYGRAMMAR', 'DISPID_SpeechObjectToken',
    'DISPID_SDKCreateKey', 'DISPID_SpeechObjectTokenCategory',
    'DISPID_SPRulesCount', 'SREPropertyStringChange', 'SRTReSent',
    'SPEI_VISEME', 'SAFTGSM610_44kHzMono', 'SPDKL_LocalMachine',
    'SAFTDefault', 'SpeechCategoryPhoneConverters', 'DISPID_SRGReset',
    'SpeechGrammarRuleStateTransitionType',
    'DISPID_SPRuleEngineConfidence', 'SRESoundEnd',
    'ISpeechRecoResultDispatch', 'DISPIDSPTSI_ActiveOffset',
    'DISPID_SpeechAudioBufferInfo', 'DISPID_SVAudioOutput',
    'SASPause', 'DISPID_SAFGetWaveFormatEx', 'SVSFDefault',
    'SPAR_Low', 'SVPAlert', 'Speech_Max_Word_Length',
    'SPEI_PROPERTY_NUM_CHANGE', 'SPSFunction', 'DISPID_SGRAddState',
    'SAFT44kHz8BitMono', 'DISPID_SOTDataKey', 'DISPID_SAVolume',
    'DISPID_SRCPause', 'SGRSTTEpsilon', 'DISPID_SRGCommit',
    'DISPID_SVEWord', 'SPEI_MIN_SR', 'DISPID_SOTs_NewEnum',
    'SAFTADPCM_8kHzMono', 'DISPID_SpeechPhraseReplacements',
    'SREStreamStart', 'SpeechCategoryRecognizers', 'SVSFParseSapi',
    'SPSTREAMFORMATTYPE', 'SPEI_FALSE_RECOGNITION',
    'eLEXTYPE_PRIVATE12', 'SPSSuppressWord', 'DISPID_SVVoice',
    'SPTEXTSELECTIONINFO', 'SPAO_NONE', 'SpMMAudioIn',
    'DISPID_SpeechLexiconPronunciation', 'DISPID_SRGId',
    'DISPID_SRGetFormat', 'DISPID_SpeechPhraseRules',
    'SAFT12kHz16BitMono', 'SPGS_EXCLUSIVE', 'SREAdaptation',
    'eLEXTYPE_RESERVED9', 'ISpeechObjectTokenCategory',
    'DISPID_SPRuleId', 'DISPID_SGRsCommit', 'SpeechMicTraining',
    'SPAS_STOP', 'DISPID_SLPLangId', 'SPSHORTCUTTYPE',
    'DISPID_SGRSTPropertyName', 'SP_VISEME_7', 'SpeechTokenKeyUI',
    'DISPID_SGRSTText', 'ISpeechMemoryStream', 'SpeechInterference',
    'eLEXTYPE_PRIVATE17', 'eLEXTYPE_RESERVED8', 'SPRST_INACTIVE',
    'ISpeechPhraseElement', 'ISpeechAudio', 'SRSActive',
    'SpeechVisemeType', 'SPEI_MAX_TTS', 'ISpeechDataKey',
    'DISPID_SRCEHypothesis', 'SVEStartInputStream', 'SP_VISEME_8',
    'SPXRO_SML', 'SpeechRecognitionType',
    'DISPID_SVAudioOutputStream',
    'DISPID_SRAllowAudioInputFormatChangesOnNextSet', 'SGRSTTWord',
    'SVEPrivate', 'DISPID_SPEEngineConfidence',
    'SPWT_LEXICAL_NO_SPECIAL_CHARS', 'SPAUDIOOPTIONS',
    'SAFT32kHz8BitMono', 'SRADynamic', 'SVP_1', 'SVSFParseAutodetect',
    'DISPID_SRCEBookmark', 'SVP_18', 'DISPID_SGRSTType',
    'DISPID_SASCurrentSeekPosition', 'DISPID_SVGetProfiles',
    'SAFT8kHz16BitMono', 'DISPID_SWFESamplesPerSec',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002', 'SPPHRASERULE',
    'SPINTERFERENCE_TOOFAST', 'eLEXTYPE_PRIVATE19',
    'DISPID_SRCESoundStart', 'SpeechEngineProperties',
    'SpeechAudioProperties', 'SPVOICESTATUS', 'SpeechFormatType',
    'DISPIDSPTSI_SelectionLength', 'DISPID_SOTGetStorageFileName',
    'SAFTCCITT_ALaw_44kHzStereo', 'SSTTTextBuffer',
    'SSSPTRelativeToStart', 'DISPID_SpeechVoiceStatus',
    'SECFNoSpecialChars', 'SP_VISEME_4', 'SpeechVoicePriority',
    'SAFT12kHz8BitStereo', 'DISPID_SRGDictationLoad',
    'DISPID_SVESentenceBoundary', 'SPSNoun', 'SpeechGrammarState',
    'ISpeechLexiconWords', 'DISPID_SRRTStreamTime',
    'SAFT11kHz16BitMono', 'SPSModifier', 'DISPID_SRCEAudioLevel',
    'DISPID_SGRsAdd', 'STCInprocHandler', 'ISpRecoGrammar',
    'SAFTADPCM_11kHzStereo', 'DISPID_SRCCreateResultFromMemory',
    'DISPID_SPEAudioTimeOffset', 'Speech_Default_Weight',
    'DISPID_SRCEStartStream', 'SGRSTTDictation', 'SSFMCreate',
    'ISpShortcut', 'DISPID_SMSAMMHandle', 'SAFT11kHz16BitStereo',
    'SGDSActive', 'DISPID_SRCESoundEnd',
    'DISPIDSPTSI_SelectionOffset', 'SAFT8kHz8BitMono',
    'DISPID_SRGetPropertyString', 'DISPID_SPIAudioSizeTime',
    'DISPID_SPISaveToMemory', 'SVP_5', 'DISPID_SDKGetStringValue',
    'DISPID_SVSInputWordPosition', 'SP_VISEME_11',
    'SPDATAKEYLOCATION', 'ISpPhraseAlt', 'SVSFPersistXML',
    'SGRSTTRule', 'DISPID_SGRAddResource', 'DISPID_SRRSpeakAudio',
    'DISPID_SpeechPhraseInfo', 'DISPID_SRGDictationSetState',
    'ISpObjectWithToken', 'SPEI_REQUEST_UI', 'SECLowConfidence',
    'DISPID_SVGetAudioInputs', 'SPEI_UNDEFINED', 'STSF_LocalAppData',
    'SpeechRecoContextState', 'DISPID_SRSNumberOfActiveRules',
    'DISPID_SRGCmdLoadFromObject', 'SpeechTokenShellFolder',
    'SpWaveFormatEx', 'SpMMAudioOut', 'ISpeechPhraseReplacements',
    'SVSFIsFilename', 'DISPID_SGRSTPropertyId', 'SPPHRASE',
    'SpeechVoiceSpeakFlags', 'DISPID_SWFEChannels',
    'DISPID_SLPSymbolic', 'DISPID_SpeechLexiconWords',
    'SpeechDiscardType', 'ISpeechPhraseElements', 'ISpProperties',
    'DISPID_SRCEFalseRecognition', 'SPDKL_DefaultLocation',
    'DISPID_SPIReplacements', 'DISPID_SPCLangId',
    'SAFT16kHz16BitMono', 'DISPID_SpeechPhraseAlternate',
    'SREFalseRecognition', 'DISPID_SDKOpenKey', 'SVP_11',
    'SGDSActiveWithAutoPause', 'DISPID_SpeechDataKey',
    'DISPID_SRCCmdMaxAlternates', 'SRERecoOtherContext',
    'DISPID_SRCAudioInInterferenceStatus', 'eLEXTYPE_PRIVATE2',
    'SPAUDIOSTATUS', 'eLEXTYPE_MORPHOLOGY', 'Library',
    'DISPID_SPPValue', 'DISPID_SVSInputSentencePosition',
    'DISPID_SGRSTsCount', 'SAFT24kHz8BitMono', 'SAFT24kHz16BitMono',
    'ISpPhrase', 'DISPID_SpeechMemoryStream', 'SRATopLevel',
    'SGSDisabled', 'DISPID_SGRSTRule', 'DISPID_SDKGetlongValue',
    'DISPID_SOTCId', 'ISpPhoneticAlphabetConverter',
    'DISPID_SPACommit', 'DISPID_SVSpeak', 'ISpDataKey',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_MS',
    'DISPID_SpeechPhraseProperties', 'SpeechCategoryRecoProfiles',
    'SPINTERFERENCE_NONE', 'DISPIDSPTSI', 'SpFileStream', 'SPPS_Verb',
    'DISPID_SRGetPropertyNumber', 'DISPID_SLGetPronunciations',
    'ISpeechRecoResultTimes', 'eLEXTYPE_PRIVATE14',
    'DISPID_SWFEFormatTag', 'ISpeechGrammarRules', 'SDTLexicalForm',
    'DISPID_SPPsItem', 'SDTAudio', 'DISPID_SOTCEnumerateTokens',
    'DISPID_SAFGuid', 'DISPID_SVEEnginePrivate', 'SREHypothesis',
    'Speech_Max_Pron_Length', 'DISPID_SPEAudioSizeBytes',
    'ISpeechRecognizerStatus', 'SDA_Consume_Leading_Spaces',
    'SPSHORTCUTPAIR', 'SpMMAudioEnum', 'SpNullPhoneConverter',
    'DISPID_SPIGetText', 'SAFTCCITT_ALaw_22kHzStereo',
    'DISPID_SPEAudioStreamOffset', 'ISpeechAudioStatus',
    'DISPID_SRState', 'DISPID_SDKSetBinaryValue',
    'DISPID_SDKDeleteKey', 'SPVPRI_ALERT', 'eLEXTYPE_PRIVATE9',
    'DISPID_SpeechRecoResult', 'ISpeechXMLRecoResult',
    'eLEXTYPE_RESERVED10', 'DISPID_SMSADeviceId',
    'SPRST_INACTIVE_WITH_PURGE', 'DISPID_SPIRetainedSizeBytes',
    'ISpeechPhraseRule', 'DISPID_SABIMinNotification',
    'DISPID_SRGRecoContext', 'DISPID_SRGDictationUnload',
    'SAFT8kHz8BitStereo', 'SpeechWordPronounceable',
    'DISPID_SRCreateRecoContext', 'eLEXTYPE_PRIVATE1', 'ISpLexicon',
    'DISPID_SPRuleFirstElement', 'SPSMF_SRGS_SAPIPROPERTIES',
    'SpeechRegistryLocalMachineRoot', 'SP_VISEME_15', 'SREStreamEnd',
    'DISPID_SRCEPropertyNumberChange',
    'DISPID_SpeechGrammarRuleStateTransition',
    'DISPID_SPRFirstElement', 'SVEEndInputStream',
    '__MIDL_IWinTypes_0009', 'SLOStatic', 'DISPID_SPRuleConfidence',
    'DISPID_SLRemovePronunciationByPhoneIds', 'SVF_Stressed',
    'SRCS_Enabled', 'ISpRecoGrammar2', 'SP_VISEME_20',
    'SRERecognition', 'SAFTText', 'DISPID_SGRSTPropertyValue',
    'SVP_15', '_ISpeechRecoContextEvents', 'eLEXTYPE_PRIVATE5',
    'SPADAPTATIONRELEVANCE', 'DISPID_SLPs_NewEnum',
    'SAFT12kHz8BitMono', 'ISpeechLexiconPronunciations',
    'DISPID_SRRTTickCount', 'SITooSlow', 'eLEXTYPE_PRIVATE18',
    'DISPID_SPRules_NewEnum', 'SPWF_INPUT',
    'DISPID_SRSCurrentStreamNumber', 'DISPID_SPAStartElementInResult',
    'SWTDeleted', 'DISPID_SRCVoicePurgeEvent', '_RemotableHandle',
    'DISPID_SGRInitialState', 'SAFT32kHz8BitStereo', 'ISpeechLexicon',
    'SAFT24kHz8BitStereo', 'SpeechRecoEvents', 'SVSFParseMask',
    'DISPID_SPIRule', 'SpeechTokenContext', 'SRSInactiveWithPurge',
    'SECFIgnoreKanaType', 'DISPID_SPPFirstElement', 'SP_VISEME_6',
    'DISPID_SVSInputWordLength', 'SpeechCategoryVoices', 'SVP_21',
    'eLEXTYPE_RESERVED4', 'SRSEIsSpeaking', 'SVP_10', 'SP_VISEME_14',
    'IInternetSecurityMgrSite', 'DISPIDSPTSI_ActiveLength',
    'SPSERIALIZEDPHRASE', 'DISPID_SpeechGrammarRule',
    'eLEXTYPE_VENDORLEXICON', 'SAFTCCITT_uLaw_8kHzStereo',
    'DISPID_SGRs_NewEnum', 'SPAUDIOBUFFERINFO', 'STCLocalServer',
    'ISpeechRecoResult', 'DISPID_SBSSeek', 'SPEI_SR_RETAINEDAUDIO',
    'SP_VISEME_5', 'SAFT32kHz16BitMono', 'SPSHORTCUTPAIRLIST',
    'SPINTERFERENCE_TOOQUIET', 'SECFIgnoreWidth', 'tagSPPROPERTYINFO',
    'SPWT_PRONUNCIATION', 'SWPUnknownWordPronounceable',
    'SDTProperty', 'SPPS_Modifier', 'SAFT12kHz16BitStereo',
    'SPEI_HYPOTHESIS', 'SPFM_CREATE_ALWAYS',
    'SPWP_UNKNOWN_WORD_PRONOUNCEABLE', 'SPEI_MIN_TTS',
    'DISPID_SGRName', 'SP_VISEME_10', 'SPEI_ACTIVE_CATEGORY_CHANGED',
    'SPAS_RUN', 'SVEAllEvents', 'SPEI_RECO_OTHER_CONTEXT',
    'DISPID_SOTCategory', 'DISPID_SOTDisplayUI', 'ISpEventSink',
    'SVSFVoiceMask', 'SPWORDPRONUNCIATIONLIST',
    'DISPID_SASFreeBufferSpace', 'ISpRecoContext', 'IEnumString',
    'SPINTERFERENCE_TOOLOUD', 'SPLEXICONTYPE', 'DISPID_SOTRemove',
    'DISPID_SpeechLexiconWord', 'SITooQuiet', 'SPRS_ACTIVE',
    'DISPID_SpeechAudio', 'DISPID_SRSAudioStatus',
    'SPXRO_Alternates_SML', 'SpeechVoiceCategoryTTSRate',
    'ISpEventSource', 'SPPS_Function', 'SGDSActiveUserDelimited',
    'SSSPTRelativeToEnd', 'ISpPhoneticAlphabetSelection',
    'SPAR_Medium', 'DISPID_SPIStartTime', 'SpeechRuleState',
    'SREPrivate', 'DISPID_SpeechRecoResultTimes', 'ISpAudio',
    'DISPIDSPRG', 'SPINTERFERENCE_NOSIGNAL',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C', 'SPEI_TTS_AUDIO_LEVEL',
    'SPLO_DYNAMIC', 'SPCT_SUB_DICTATION', 'SPXMLRESULTOPTIONS',
    'SSFMCreateForWrite', 'DISPID_SpeechRecoContextEvents',
    'DISPID_SBSRead', 'DISPID_SpeechXMLRecoResult',
    'SAFTADPCM_22kHzMono', 'SpObjectTokenCategory',
    'ISpObjectTokenCategory', 'tagSTATSTG', 'SpeechAudioFormatType',
    'DISPID_SOTId', 'SpeechTokenIdUserLexicon',
    'SpeechBookmarkOptions', 'eWORDTYPE_DELETED', 'SSFMOpenForRead',
    'ISpRecoResult', 'ISpNotifySink', 'SP_VISEME_17',
    'SVSFUnusedFlags', 'SPAR_High', 'SRTExtendableParse',
    'SPEI_INTERFERENCE', 'SpeechVoiceEvents', 'DISPID_SRRecognizer',
    'SpeechGrammarTagDictation', 'SGRSTTWildcard', 'DISPID_SLPsItem',
    'DISPID_SRRGetXMLResult', 'SPRULE', 'ISpGrammarBuilder',
    'SpeechDataKeyLocation', 'SRAONone', 'ISpeechPhraseRules',
    'SRESoundStart', 'SVSFlagsAsync', 'SVP_9', 'SPLO_STATIC',
    'DISPID_SWFEExtraData', 'SVF_None',
    'DISPID_SPPBRestorePhraseFromMemory', 'SRSActiveAlways',
    'SPINTERFERENCE_LATENCY_WARNING', 'SPSHT_NotOverriden',
    'eLEXTYPE_RESERVED7', 'SPAO_RETAIN_AUDIO',
    'SpeechAudioFormatGUIDWave', 'DISPID_SABIEventBias', 'SINoSignal',
    'SRERequestUI', 'SpeechTokenValueCLSID',
    'DISPID_SPEActualConfidence', 'SDKLDefaultLocation',
    'DISPID_SRGState', 'DISPID_SVResume',
    'DISPID_SpeechPhoneConverter', 'SpAudioFormat', 'SP_VISEME_13',
    'DISPID_SRRAudioFormat', 'DISPID_SPILanguageId',
    'SAFT44kHz16BitStereo', 'DISPID_SPAsCount', 'SASClosed',
    'DISPID_SGRSTransitions', 'DISPID_SPPChildren',
    'SpeechPropertyAdaptationOn', 'DISPID_SRCEPhraseStart',
    'SWPKnownWordPronounceable', 'SpeechGrammarWordType',
    'DISPID_SLAddPronunciationByPhoneIds', 'eLEXTYPE_PRIVATE15'
]

