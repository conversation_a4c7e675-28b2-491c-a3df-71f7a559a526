"""
Login Dialog for TikTok Automation Tool
Professional authentication interface with registration and password recovery.
"""

import logging
from typing import Optional

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QLineEdit, QTabWidget, QWidget, QCheckBox, QMessageBox, QProgressBar,
    QFrame, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap, QPalette, QIcon

from utils.security import AuthenticationManager

class LoginWorker(QThread):
    """Worker thread for authentication operations."""
    
    login_result = pyqtSignal(bool, str)  # success, message
    register_result = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, auth_manager: AuthenticationManager):
        super().__init__()
        self.auth_manager = auth_manager
        self.operation = None
        self.username = None
        self.password = None
        self.email = None
    
    def login(self, username: str, password: str):
        """Perform login operation."""
        self.operation = "login"
        self.username = username
        self.password = password
        self.start()
    
    def register(self, username: str, password: str, email: str = None):
        """Perform registration operation."""
        self.operation = "register"
        self.username = username
        self.password = password
        self.email = email
        self.start()
    
    def run(self):
        """Execute authentication operation."""
        try:
            if self.operation == "login":
                session_token = self.auth_manager.authenticate_user(self.username, self.password)
                if session_token:
                    self.login_result.emit(True, "Login successful!")
                else:
                    self.login_result.emit(False, "Invalid username or password.")
            
            elif self.operation == "register":
                success = self.auth_manager.register_user(self.username, self.password, self.email)
                if success:
                    self.register_result.emit(True, "Registration successful!")
                else:
                    self.register_result.emit(False, "Registration failed. Username may already exist.")
        
        except Exception as e:
            logging.error(f"Authentication operation failed: {e}")
            if self.operation == "login":
                self.login_result.emit(False, f"Login error: {e}")
            else:
                self.register_result.emit(False, f"Registration error: {e}")

class LoginDialog(QDialog):
    """Professional login dialog with modern TikTok styling."""
    
    login_successful = pyqtSignal(str)  # username
    
    def __init__(self, auth_manager: AuthenticationManager, parent=None):
        """Initialize login dialog.
        
        Args:
            auth_manager: Authentication manager instance
            parent: Parent widget
        """
        super().__init__(parent)
        
        self.auth_manager = auth_manager
        self.session_token = None
        
        # Setup dialog
        self.setWindowTitle("TikTok Automation Tool - Authentication")
        self.setMinimumSize(750, 800)
        self.resize(750, 800)
        self.setModal(True)
        
        # Center on screen
        self._center_on_screen()
        
        # Initialize UI
        self._init_ui()
        self._setup_worker()
        
        # Apply styling
        self._apply_styling()
        
        logging.info("Login dialog initialized")
    
    def _center_on_screen(self):
        """Center dialog on screen."""
        screen = self.screen().availableGeometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
    
    def _init_ui(self):
        """Initialize user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Header
        header_widget = self._create_header()
        layout.addWidget(header_widget)
        
        # Tab widget for login/register
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # Login tab
        login_tab = self._create_login_tab()
        self.tab_widget.addTab(login_tab, "🔐 Login")
        
        # Register tab
        register_tab = self._create_register_tab()
        self.tab_widget.addTab(register_tab, "📝 Register")
        
        layout.addWidget(self.tab_widget)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Footer
        footer_widget = self._create_footer()
        layout.addWidget(footer_widget)
    
    def _create_header(self) -> QWidget:
        """Create header section."""
        header_widget = QWidget()
        layout = QVBoxLayout(header_widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(10)
        
        # Logo/Icon
        logo_label = QLabel("🎵")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 48px;")
        layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel("TikTok Automation Tool")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setProperty("class", "title")
        layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Professional Automation Platform")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setProperty("class", "subtitle")
        layout.addWidget(subtitle_label)
        
        return header_widget
    
    def _create_login_tab(self) -> QWidget:
        """Create login tab."""
        login_widget = QWidget()
        layout = QVBoxLayout(login_widget)
        layout.setSpacing(20)
        
        # Login form - EXTRA WIDE LAYOUT
        form_layout = QGridLayout()
        form_layout.setSpacing(25)
        form_layout.setColumnStretch(0, 0)  # Label column fixed
        form_layout.setColumnStretch(1, 3)  # Input column much wider
        form_layout.setColumnMinimumWidth(1, 400)  # Force minimum width

        # Username
        username_label = QLabel("Username:")
        username_label.setMinimumWidth(140)
        username_label.setMaximumWidth(140)
        form_layout.addWidget(username_label, 0, 0, Qt.AlignTop)
        self.login_username = QLineEdit()
        self.login_username.setPlaceholderText("Enter your username")
        self.login_username.setMinimumWidth(400)
        self.login_username.setMinimumHeight(35)
        self.login_username.returnPressed.connect(self._handle_login)
        form_layout.addWidget(self.login_username, 0, 1)

        # Password
        password_label = QLabel("Password:")
        password_label.setMinimumWidth(140)
        password_label.setMaximumWidth(140)
        form_layout.addWidget(password_label, 1, 0, Qt.AlignTop)
        self.login_password = QLineEdit()
        self.login_password.setPlaceholderText("Enter your password")
        self.login_password.setEchoMode(QLineEdit.Password)
        self.login_password.setMinimumWidth(400)
        self.login_password.setMinimumHeight(35)
        self.login_password.returnPressed.connect(self._handle_login)
        form_layout.addWidget(self.login_password, 1, 1)
        
        layout.addLayout(form_layout)
        
        # Remember me checkbox
        self.remember_checkbox = QCheckBox("Remember me")
        layout.addWidget(self.remember_checkbox)
        
        # Login button
        self.login_button = QPushButton("🚀 Login")
        self.login_button.setProperty("class", "primary")
        self.login_button.clicked.connect(self._handle_login)
        layout.addWidget(self.login_button)
        
        # Forgot password link
        forgot_button = QPushButton("Forgot Password?")
        forgot_button.setProperty("class", "link")
        forgot_button.clicked.connect(self._show_forgot_password)
        layout.addWidget(forgot_button)
        
        layout.addStretch()
        
        return login_widget
    
    def _create_register_tab(self) -> QWidget:
        """Create registration tab."""
        register_widget = QWidget()
        layout = QVBoxLayout(register_widget)
        layout.setSpacing(20)
        
        # Registration form - PERFECT SPACING LAYOUT
        form_layout = QGridLayout()
        form_layout.setSpacing(30)  # Extra spacing between rows
        form_layout.setHorizontalSpacing(40)  # Extra spacing between columns
        form_layout.setVerticalSpacing(30)  # Extra vertical spacing
        form_layout.setColumnStretch(0, 0)  # Label column fixed
        form_layout.setColumnStretch(1, 4)  # Input column much wider
        form_layout.setColumnMinimumWidth(0, 160)  # Label column minimum
        form_layout.setColumnMinimumWidth(1, 450)  # Input column minimum

        # Username
        username_label = QLabel("Username:")
        username_label.setFixedWidth(150)
        username_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.addWidget(username_label, 0, 0)
        self.register_username = QLineEdit()
        self.register_username.setPlaceholderText("Choose a username")
        self.register_username.setMinimumWidth(450)
        self.register_username.setMinimumHeight(40)
        form_layout.addWidget(self.register_username, 0, 1)

        # Email
        email_label = QLabel("Email:")
        email_label.setFixedWidth(150)
        email_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.addWidget(email_label, 1, 0)
        self.register_email = QLineEdit()
        self.register_email.setPlaceholderText("Enter your email (optional)")
        self.register_email.setMinimumWidth(450)
        self.register_email.setMinimumHeight(40)
        form_layout.addWidget(self.register_email, 1, 1)

        # Password
        password_label = QLabel("Password:")
        password_label.setFixedWidth(150)
        password_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.addWidget(password_label, 2, 0)
        self.register_password = QLineEdit()
        self.register_password.setPlaceholderText("Create a strong password")
        self.register_password.setEchoMode(QLineEdit.Password)
        self.register_password.setMinimumWidth(450)
        self.register_password.setMinimumHeight(40)
        form_layout.addWidget(self.register_password, 2, 1)

        # Confirm password
        confirm_label = QLabel("Confirm:")
        confirm_label.setFixedWidth(150)
        confirm_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.addWidget(confirm_label, 3, 0)
        self.register_confirm = QLineEdit()
        self.register_confirm.setPlaceholderText("Confirm your password")
        self.register_confirm.setEchoMode(QLineEdit.Password)
        self.register_confirm.setMinimumWidth(450)
        self.register_confirm.setMinimumHeight(40)
        self.register_confirm.returnPressed.connect(self._handle_register)
        form_layout.addWidget(self.register_confirm, 3, 1)
        
        layout.addLayout(form_layout)
        
        # Password requirements
        requirements_label = QLabel(
            "Password Requirements:\n"
            "• At least 8 characters\n"
            "• Uppercase and lowercase letters\n"
            "• At least one number\n"
            "• At least one special character"
        )
        requirements_label.setProperty("class", "description")
        layout.addWidget(requirements_label)
        
        # Terms checkbox
        self.terms_checkbox = QCheckBox("I agree to the Terms of Service and Privacy Policy")
        layout.addWidget(self.terms_checkbox)
        
        # Register button
        self.register_button = QPushButton("📝 Create Account")
        self.register_button.setProperty("class", "primary")
        self.register_button.clicked.connect(self._handle_register)
        layout.addWidget(self.register_button)
        
        layout.addStretch()
        
        return register_widget
    
    def _create_footer(self) -> QWidget:
        """Create footer section."""
        footer_widget = QWidget()
        layout = QHBoxLayout(footer_widget)
        layout.setContentsMargins(0, 10, 0, 0)
        
        # Guest mode button
        guest_button = QPushButton("Continue as Guest")
        guest_button.setProperty("class", "secondary")
        guest_button.clicked.connect(self._continue_as_guest)
        layout.addWidget(guest_button)
        
        layout.addStretch()
        
        # Cancel button
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        layout.addWidget(cancel_button)
        
        return footer_widget
    
    def _setup_worker(self):
        """Setup authentication worker thread."""
        self.worker = LoginWorker(self.auth_manager)
        self.worker.login_result.connect(self._on_login_result)
        self.worker.register_result.connect(self._on_register_result)
    
    def _apply_styling(self):
        """Apply custom styling to dialog."""
        self.setStyleSheet("""
            QDialog {
                background-color: #000000;
                color: #FFFFFF;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QLabel[class="title"] {
                font-size: 24px;
                font-weight: 700;
                color: #FF0050;
                margin: 10px 0;
            }
            
            QLabel[class="subtitle"] {
                font-size: 16px;
                font-weight: 500;
                color: #25F4EE;
                margin-bottom: 20px;
            }
            
            QLabel[class="description"] {
                font-size: 11px;
                color: #A8A8B3;
                line-height: 1.4;
                margin: 10px 0;
            }
            
            QLineEdit {
                background-color: #161823;
                border: 2px solid #2F2F35;
                border-radius: 8px;
                padding: 15px;
                color: #FFFFFF;
                font-size: 15px;
                min-height: 25px;
                min-width: 300px;
            }
            
            QLineEdit:focus {
                border-color: #FF0050;
                outline: none;
            }
            
            QPushButton[class="primary"] {
                background-color: #FF0050;
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: 600;
                min-height: 20px;
            }
            
            QPushButton[class="primary"]:hover {
                background-color: #E6004A;
            }
            
            QPushButton[class="primary"]:pressed {
                background-color: #CC0042;
            }
            
            QPushButton[class="secondary"] {
                background-color: #25F4EE;
                color: #000000;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: 600;
            }
            
            QPushButton[class="secondary"]:hover {
                background-color: #1FD4C9;
            }
            
            QPushButton[class="link"] {
                background-color: transparent;
                color: #25F4EE;
                border: none;
                text-decoration: underline;
                font-size: 12px;
            }
            
            QPushButton[class="link"]:hover {
                color: #FF0050;
            }
            
            QCheckBox {
                color: #FFFFFF;
                font-size: 12px;
            }
            
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #2F2F35;
                border-radius: 3px;
                background-color: #161823;
            }
            
            QCheckBox::indicator:checked {
                background-color: #FF0050;
                border-color: #FF0050;
            }
            
            QTabWidget::pane {
                background-color: #161823;
                border: 2px solid #2F2F35;
                border-radius: 8px;
                margin-top: 10px;
            }
            
            QTabBar::tab {
                background-color: #2F2F35;
                color: #A8A8B3;
                padding: 12px 24px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 600;
            }
            
            QTabBar::tab:selected {
                background-color: #FF0050;
                color: #FFFFFF;
            }
            
            QTabBar::tab:hover {
                background-color: #25F4EE;
                color: #000000;
            }
            
            QProgressBar {
                background-color: #161823;
                border: 2px solid #2F2F35;
                border-radius: 8px;
                text-align: center;
                color: #FFFFFF;
                font-weight: 600;
                height: 20px;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF0050, stop:1 #25F4EE);
                border-radius: 6px;
            }
        """)
    
    def _handle_login(self):
        """Handle login button click."""
        username = self.login_username.text().strip()
        password = self.login_password.text()
        
        if not username or not password:
            QMessageBox.warning(self, "Login Error", "Please enter both username and password.")
            return
        
        # Show progress
        self._set_loading(True)
        
        # Start authentication
        self.worker.login(username, password)
    
    def _handle_register(self):
        """Handle register button click."""
        username = self.register_username.text().strip()
        email = self.register_email.text().strip()
        password = self.register_password.text()
        confirm = self.register_confirm.text()
        
        # Validation
        if not username or not password:
            QMessageBox.warning(self, "Registration Error", "Please enter username and password.")
            return
        
        if password != confirm:
            QMessageBox.warning(self, "Registration Error", "Passwords do not match.")
            return
        
        if not self.terms_checkbox.isChecked():
            QMessageBox.warning(self, "Registration Error", "Please agree to the Terms of Service.")
            return
        
        # Show progress
        self._set_loading(True)
        
        # Start registration
        self.worker.register(username, password, email if email else None)
    
    def _on_login_result(self, success: bool, message: str):
        """Handle login result."""
        self._set_loading(False)
        
        if success:
            username = self.login_username.text().strip()
            self.login_successful.emit(username)
            self.accept()
        else:
            QMessageBox.critical(self, "Login Failed", message)
    
    def _on_register_result(self, success: bool, message: str):
        """Handle registration result."""
        self._set_loading(False)
        
        if success:
            QMessageBox.information(
                self, 
                "Registration Successful", 
                "Account created successfully! You can now login."
            )
            # Switch to login tab
            self.tab_widget.setCurrentIndex(0)
            # Pre-fill username
            self.login_username.setText(self.register_username.text())
        else:
            QMessageBox.critical(self, "Registration Failed", message)
    
    def _set_loading(self, loading: bool):
        """Set loading state."""
        self.progress_bar.setVisible(loading)
        if loading:
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        # Disable/enable buttons
        self.login_button.setEnabled(not loading)
        self.register_button.setEnabled(not loading)
    
    def _show_forgot_password(self):
        """Show forgot password dialog."""
        QMessageBox.information(
            self,
            "Password Recovery",
            "Password recovery feature will be implemented in a future version.\n\n"
            "For now, please contact support if you need to reset your password."
        )
    
    def _continue_as_guest(self):
        """Continue without authentication."""
        reply = QMessageBox.question(
            self,
            "Guest Mode",
            "Continue without authentication?\n\n"
            "Note: Some features may be limited in guest mode.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.login_successful.emit("guest")
            self.accept()
