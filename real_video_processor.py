#!/usr/bin/env python3
"""
Real Video Processor - Actually combines TTS audio with video
"""

import os
import subprocess
import tempfile
import json
import time
from pathlib import Path

class RealVideoProcessor:
    """
    Real video processor that actually combines TTS audio with video files
    """
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="video_tts_")
        self.ffmpeg_path = self.find_ffmpeg()
        
    def find_ffmpeg(self):
        """Try to find FFmpeg executable"""
        possible_paths = [
            'ffmpeg',
            'ffmpeg.exe',
            r'C:\ffmpeg\bin\ffmpeg.exe',
            r'C:\Program Files\ffmpeg\bin\ffmpeg.exe',
            '/usr/bin/ffmpeg',
            '/usr/local/bin/ffmpeg'
        ]
        
        for path in possible_paths:
            try:
                result = subprocess.run([path, '-version'], 
                                      capture_output=True, 
                                      text=True, 
                                      timeout=5)
                if result.returncode == 0:
                    print(f"✅ Found FFmpeg: {path}")
                    return path
            except:
                continue
        
        print("⚠️ FFmpeg not found - will use basic audio overlay")
        return None
    
    def combine_video_with_tts(self, video_path, audio_files, output_path, 
                              background_volume=0.3, tts_volume=1.0):
        """
        Combine video with TTS audio files
        """
        try:
            print(f"🎬 Combining video with {len(audio_files)} TTS audio files")
            
            if self.ffmpeg_path:
                return self._combine_with_ffmpeg(video_path, audio_files, output_path, 
                                               background_volume, tts_volume)
            else:
                return self._combine_with_basic_method(video_path, audio_files, output_path)
                
        except Exception as e:
            print(f"❌ Error combining video: {e}")
            return False
    
    def _combine_with_ffmpeg(self, video_path, audio_files, output_path, 
                           background_volume, tts_volume):
        """Use FFmpeg for professional video/audio combination"""
        try:
            # Create a temporary audio file by concatenating all TTS files
            combined_audio = os.path.join(self.temp_dir, "combined_tts.wav")
            
            if len(audio_files) == 1:
                # Single audio file - just copy
                import shutil
                shutil.copy2(audio_files[0]['file'], combined_audio)
            else:
                # Multiple audio files - need to position them correctly
                self._create_positioned_audio(audio_files, combined_audio)
            
            # FFmpeg command to combine video with audio
            cmd = [
                self.ffmpeg_path,
                '-i', video_path,           # Input video
                '-i', combined_audio,       # Input TTS audio
                '-filter_complex', 
                f'[0:a]volume={background_volume}[bg];[1:a]volume={tts_volume}[tts];[bg][tts]amix=inputs=2:duration=first[out]',
                '-map', '0:v',              # Use video from first input
                '-map', '[out]',            # Use mixed audio
                '-c:v', 'copy',             # Copy video codec (faster)
                '-c:a', 'aac',              # Use AAC audio codec
                '-y',                       # Overwrite output file
                output_path
            ]
            
            print(f"🔧 Running FFmpeg command...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ Video successfully created: {output_path}")
                return True
            else:
                print(f"❌ FFmpeg error: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ FFmpeg processing error: {e}")
            return False
    
    def _create_positioned_audio(self, audio_files, output_path):
        """Create audio file with TTS positioned at correct times"""
        try:
            # Get total duration from the last audio file
            total_duration = max(af['end_time'] for af in audio_files)
            
            # Create silent base audio
            silent_audio = os.path.join(self.temp_dir, "silent_base.wav")
            cmd = [
                self.ffmpeg_path,
                '-f', 'lavfi',
                '-i', f'anullsrc=channel_layout=stereo:sample_rate=44100',
                '-t', str(total_duration),
                '-y',
                silent_audio
            ]
            subprocess.run(cmd, capture_output=True, timeout=60)
            
            # Overlay each TTS audio at the correct time
            current_input = silent_audio
            
            for i, audio_file in enumerate(audio_files):
                temp_output = os.path.join(self.temp_dir, f"overlay_{i}.wav")
                
                cmd = [
                    self.ffmpeg_path,
                    '-i', current_input,
                    '-i', audio_file['file'],
                    '-filter_complex', f'[0:a][1:a]amix=inputs=2:duration=first:dropout_transition=0',
                    '-y',
                    temp_output
                ]
                
                result = subprocess.run(cmd, capture_output=True, timeout=60)
                if result.returncode == 0:
                    current_input = temp_output
                else:
                    print(f"⚠️ Warning: Could not overlay audio {i}")
            
            # Copy final result
            import shutil
            shutil.copy2(current_input, output_path)
            return True
            
        except Exception as e:
            print(f"❌ Error creating positioned audio: {e}")
            return False
    
    def _combine_with_basic_method(self, video_path, audio_files, output_path):
        """Basic method without FFmpeg - just copy video and create audio file"""
        try:
            print("⚠️ Using basic method - creating separate audio file")
            
            # Just copy the video file
            import shutil
            shutil.copy2(video_path, output_path)
            
            # Create a combined audio file for reference
            if audio_files:
                audio_output = output_path.replace('.mp4', '_audio.wav')
                if len(audio_files) == 1:
                    shutil.copy2(audio_files[0]['file'], audio_output)
                    print(f"📁 Audio saved separately: {audio_output}")
            
            return True
            
        except Exception as e:
            print(f"❌ Basic method error: {e}")
            return False
    
    def install_ffmpeg_instructions(self):
        """Provide instructions for installing FFmpeg"""
        instructions = """
        🔧 FFmpeg Installation Instructions:
        
        Windows:
        1. Download FFmpeg from: https://ffmpeg.org/download.html
        2. Extract to C:\\ffmpeg\\
        3. Add C:\\ffmpeg\\bin to your PATH environment variable
        
        Or use Chocolatey:
        choco install ffmpeg
        
        macOS:
        brew install ffmpeg
        
        Linux (Ubuntu/Debian):
        sudo apt update && sudo apt install ffmpeg
        """
        return instructions
    
    def cleanup(self):
        """Clean up temporary files"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            print("🧹 Temporary files cleaned up")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")

# Test function
def test_video_processor():
    """Test the video processor"""
    processor = RealVideoProcessor()
    
    print("🧪 Testing Video Processor")
    print(f"FFmpeg available: {processor.ffmpeg_path is not None}")
    
    if not processor.ffmpeg_path:
        print(processor.install_ffmpeg_instructions())
    
    processor.cleanup()

if __name__ == "__main__":
    test_video_processor()
