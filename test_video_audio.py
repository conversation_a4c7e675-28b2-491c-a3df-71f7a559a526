#!/usr/bin/env python3
"""
Test script to verify video + audio combination works
"""

import os
import tempfile
from audio_video_combiner import AudioVideoCombiner
import time

def test_video_audio_combination():
    """Test the video audio combination"""
    print("🧪 Testing Video + Audio Combination")
    
    # Create combiner
    combiner = AudioVideoCombiner()
    
    # Test creating a demo video
    print("📹 Creating demo video...")
    try:
        from moviepy.editor import ColorClip
        
        # Create a simple test video
        test_video_path = os.path.join(tempfile.gettempdir(), "test_video.mp4")
        clip = ColorClip(size=(640, 480), color=(100, 150, 200), duration=10)
        clip.write_videofile(
            test_video_path,
            fps=24,
            codec='libx264',
            audio_codec='aac',
            verbose=False,
            logger=None
        )
        clip.close()
        
        print(f"✅ Demo video created: {test_video_path}")
        print(f"📊 Video size: {os.path.getsize(test_video_path) / 1024:.1f} KB")
        
        # Test getting video info
        info = combiner.get_video_info(test_video_path)
        print(f"📹 Video info: {info}")
        
        # Create fake audio segments for testing
        audio_segments = [
            {
                'file': 'fake_audio1.wav',
                'start_time': 0,
                'end_time': 5,
                'text': 'Test audio segment 1'
            },
            {
                'file': 'fake_audio2.wav', 
                'start_time': 5,
                'end_time': 10,
                'text': 'Test audio segment 2'
            }
        ]
        
        # Test output path
        output_path = os.path.join(tempfile.gettempdir(), "test_output.mp4")
        
        print("🎬 Testing video combination (will fail due to fake audio files)...")
        success = combiner.combine_audio_with_video(
            video_path=test_video_path,
            audio_segments=audio_segments,
            output_path=output_path,
            background_volume=0.3,
            tts_volume=1.0
        )
        
        if success:
            print("✅ Video combination test passed!")
        else:
            print("⚠️ Video combination failed (expected due to fake audio)")
        
        # Cleanup
        combiner.cleanup()
        
        # Clean up test files
        if os.path.exists(test_video_path):
            os.remove(test_video_path)
            print("🧹 Test video cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_video_audio_combination()
