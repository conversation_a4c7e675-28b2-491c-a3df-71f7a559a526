"""
Professional Splash Screen for TikTok Automation Tool
Modern loading screen with TikTok branding and progress indication.
"""

import time
from typing import Optional

from PyQt5.QtWidgets import (
    QSplashScreen, QLabel, QProgressBar, QVBoxLayout, QHBoxLayout,
    QWidget, QApplication
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QPixmap, QPainter, QFont, QColor, QLinearGradient

class LoadingWorker(QThread):
    """Worker thread for loading operations."""
    
    progress_updated = pyqtSignal(int, str)  # progress, message
    loading_finished = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.loading_steps = [
            (10, "Initializing application..."),
            (20, "Loading configuration..."),
            (30, "Setting up security..."),
            (40, "Initializing automation engine..."),
            (50, "Loading proxy manager..."),
            (60, "Setting up captcha solver..."),
            (70, "Applying theme..."),
            (80, "Preparing user interface..."),
            (90, "Finalizing setup..."),
            (100, "Ready to launch!")
        ]
    
    def run(self):
        """Execute loading sequence."""
        for progress, message in self.loading_steps:
            self.progress_updated.emit(progress, message)
            time.sleep(0.3)  # Simulate loading time
        
        self.loading_finished.emit()

class SplashScreen(QSplashScreen):
    """Professional splash screen with TikTok branding."""
    
    loading_finished = pyqtSignal()
    
    def __init__(self):
        """Initialize splash screen."""
        # Create splash pixmap
        pixmap = self._create_splash_pixmap()
        super().__init__(pixmap)
        
        # Setup splash screen
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Initialize UI components
        self._init_ui()
        
        # Setup loading worker
        self.worker = LoadingWorker()
        self.worker.progress_updated.connect(self._update_progress)
        self.worker.loading_finished.connect(self._on_loading_finished)
        
        # Auto-close timer (fallback)
        self.close_timer = QTimer()
        self.close_timer.timeout.connect(self._on_loading_finished)
        self.close_timer.setSingleShot(True)
        self.close_timer.start(5000)  # 5 seconds max
    
    def _create_splash_pixmap(self) -> QPixmap:
        """Create splash screen pixmap with TikTok branding.
        
        Returns:
            QPixmap for splash screen
        """
        width, height = 500, 350
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Background gradient
        gradient = QLinearGradient(0, 0, 0, height)
        gradient.setColorAt(0, QColor(0, 0, 0, 240))
        gradient.setColorAt(0.5, QColor(22, 24, 35, 240))
        gradient.setColorAt(1, QColor(0, 0, 0, 240))
        
        painter.setBrush(gradient)
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, width, height, 20, 20)
        
        # Border
        painter.setPen(QColor(255, 0, 80, 150))
        painter.setBrush(Qt.NoBrush)
        painter.drawRoundedRect(2, 2, width-4, height-4, 18, 18)
        
        # TikTok logo area (simplified)
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor(255, 0, 80))
        
        # Draw stylized TikTok logo
        logo_x, logo_y = width // 2 - 30, 60
        painter.drawEllipse(logo_x, logo_y, 60, 60)
        
        painter.setBrush(QColor(37, 244, 238))
        painter.drawEllipse(logo_x + 10, logo_y + 10, 40, 40)
        
        # Title text
        painter.setPen(QColor(255, 255, 255))
        title_font = QFont("Segoe UI", 24, QFont.Bold)
        painter.setFont(title_font)
        
        title_rect = painter.fontMetrics().boundingRect("TikTok Automation")
        title_x = (width - title_rect.width()) // 2
        painter.drawText(title_x, 160, "TikTok Automation")
        
        # Subtitle
        painter.setPen(QColor(37, 244, 238))
        subtitle_font = QFont("Segoe UI", 14, QFont.Normal)
        painter.setFont(subtitle_font)
        
        subtitle_rect = painter.fontMetrics().boundingRect("Professional Automation Tool")
        subtitle_x = (width - subtitle_rect.width()) // 2
        painter.drawText(subtitle_x, 185, "Professional Automation Tool")
        
        # Version
        painter.setPen(QColor(168, 168, 179))
        version_font = QFont("Segoe UI", 10)
        painter.setFont(version_font)
        
        version_rect = painter.fontMetrics().boundingRect("Version 1.0.0")
        version_x = (width - version_rect.width()) // 2
        painter.drawText(version_x, 205, "Version 1.0.0")
        
        painter.end()
        return pixmap
    
    def _init_ui(self):
        """Initialize UI components on splash screen."""
        # Create widget for progress bar
        self.progress_widget = QWidget(self)
        self.progress_widget.setGeometry(50, 280, 400, 50)
        
        layout = QVBoxLayout(self.progress_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # Status label
        self.status_label = QLabel("Initializing...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 12px;
                font-weight: 500;
                background: transparent;
            }
        """)
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedHeight(6)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: rgba(47, 47, 53, 180);
                border: none;
                border-radius: 3px;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF0050, stop:0.5 #FF4081, stop:1 #25F4EE);
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # Copyright label
        self.copyright_label = QLabel("© 2025 TikTok Automation Tool - Educational Use Only")
        self.copyright_label.setAlignment(Qt.AlignCenter)
        self.copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(168, 168, 179, 180);
                font-size: 9px;
                background: transparent;
                margin-top: 10px;
            }
        """)
        self.copyright_label.setGeometry(50, 320, 400, 20)
    
    def start_loading(self):
        """Start the loading process."""
        self.worker.start()
    
    def _update_progress(self, progress: int, message: str):
        """Update progress bar and status message.
        
        Args:
            progress: Progress percentage (0-100)
            message: Status message
        """
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
        
        # Update splash screen message
        self.showMessage(
            message,
            Qt.AlignBottom | Qt.AlignHCenter,
            QColor(255, 255, 255)
        )
        
        # Process events to update UI
        QApplication.processEvents()
    
    def _on_loading_finished(self):
        """Handle loading completion."""
        self.close_timer.stop()
        self.loading_finished.emit()
        
        # Fade out effect (optional)
        self.hide()
    
    def mousePressEvent(self, event):
        """Handle mouse press events (prevent closing on click)."""
        # Ignore mouse clicks during loading
        pass
    
    def keyPressEvent(self, event):
        """Handle key press events (prevent closing on key press)."""
        # Ignore key presses during loading
        pass

class ModernSplashScreen(QSplashScreen):
    """Alternative modern splash screen design."""
    
    loading_finished = pyqtSignal()
    
    def __init__(self):
        """Initialize modern splash screen."""
        # Create modern splash pixmap
        pixmap = self._create_modern_pixmap()
        super().__init__(pixmap)
        
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Setup loading
        self.progress = 0
        self.status_text = "Loading..."
        
        # Animation timer
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self._animate)
        self.animation_timer.start(50)  # 20 FPS
        
        # Loading timer
        self.loading_timer = QTimer()
        self.loading_timer.timeout.connect(self._update_loading)
        self.loading_timer.start(100)  # Update every 100ms
    
    def _create_modern_pixmap(self) -> QPixmap:
        """Create modern splash screen pixmap.
        
        Returns:
            QPixmap for splash screen
        """
        width, height = 600, 400
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Modern gradient background
        gradient = QLinearGradient(0, 0, width, height)
        gradient.setColorAt(0, QColor(0, 0, 0, 250))
        gradient.setColorAt(0.3, QColor(22, 24, 35, 250))
        gradient.setColorAt(0.7, QColor(47, 47, 53, 250))
        gradient.setColorAt(1, QColor(0, 0, 0, 250))
        
        painter.setBrush(gradient)
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, width, height, 25, 25)
        
        # Accent border
        border_gradient = QLinearGradient(0, 0, width, 0)
        border_gradient.setColorAt(0, QColor(255, 0, 80))
        border_gradient.setColorAt(0.5, QColor(255, 64, 129))
        border_gradient.setColorAt(1, QColor(37, 244, 238))
        
        painter.setPen(QColor(255, 0, 80, 200))
        painter.setBrush(Qt.NoBrush)
        painter.drawRoundedRect(3, 3, width-6, height-6, 22, 22)
        
        painter.end()
        return pixmap
    
    def _animate(self):
        """Animate splash screen elements."""
        self.update()
    
    def _update_loading(self):
        """Update loading progress."""
        self.progress += 2
        if self.progress >= 100:
            self.loading_timer.stop()
            self.animation_timer.stop()
            self.loading_finished.emit()
            self.hide()
    
    def paintEvent(self, event):
        """Custom paint event for animations."""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw animated elements
        self._draw_logo(painter)
        self._draw_text(painter)
        self._draw_progress(painter)
        
        painter.end()
    
    def _draw_logo(self, painter: QPainter):
        """Draw animated logo."""
        center_x, center_y = self.width() // 2, 120
        
        # Pulsing effect
        pulse = abs(time.time() * 2) % 2
        scale = 1.0 + pulse * 0.1
        
        painter.save()
        painter.translate(center_x, center_y)
        painter.scale(scale, scale)
        
        # Draw stylized logo
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor(255, 0, 80))
        painter.drawEllipse(-25, -25, 50, 50)
        
        painter.setBrush(QColor(37, 244, 238))
        painter.drawEllipse(-15, -15, 30, 30)
        
        painter.restore()
    
    def _draw_text(self, painter: QPainter):
        """Draw title and subtitle text."""
        # Title
        painter.setPen(QColor(255, 255, 255))
        title_font = QFont("Segoe UI", 28, QFont.Bold)
        painter.setFont(title_font)
        
        title_rect = painter.fontMetrics().boundingRect("TikTok Automation")
        title_x = (self.width() - title_rect.width()) // 2
        painter.drawText(title_x, 200, "TikTok Automation")
        
        # Subtitle with gradient effect
        painter.setPen(QColor(37, 244, 238))
        subtitle_font = QFont("Segoe UI", 16)
        painter.setFont(subtitle_font)
        
        subtitle_rect = painter.fontMetrics().boundingRect("Professional Tool")
        subtitle_x = (self.width() - subtitle_rect.width()) // 2
        painter.drawText(subtitle_x, 230, "Professional Tool")
    
    def _draw_progress(self, painter: QPainter):
        """Draw animated progress indicator."""
        # Progress bar
        bar_width = 300
        bar_height = 4
        bar_x = (self.width() - bar_width) // 2
        bar_y = 280
        
        # Background
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor(47, 47, 53, 150))
        painter.drawRoundedRect(bar_x, bar_y, bar_width, bar_height, 2, 2)
        
        # Progress
        progress_width = int((self.progress / 100) * bar_width)
        if progress_width > 0:
            gradient = QLinearGradient(bar_x, 0, bar_x + progress_width, 0)
            gradient.setColorAt(0, QColor(255, 0, 80))
            gradient.setColorAt(1, QColor(37, 244, 238))
            
            painter.setBrush(gradient)
            painter.drawRoundedRect(bar_x, bar_y, progress_width, bar_height, 2, 2)
        
        # Status text
        painter.setPen(QColor(255, 255, 255))
        status_font = QFont("Segoe UI", 12)
        painter.setFont(status_font)
        
        status_rect = painter.fontMetrics().boundingRect(self.status_text)
        status_x = (self.width() - status_rect.width()) // 2
        painter.drawText(status_x, 310, self.status_text)
