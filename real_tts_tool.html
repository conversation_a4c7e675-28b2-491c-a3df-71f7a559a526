<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Werkende AI Voice Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .step h3 {
            color: #555;
            margin-top: 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            resize: vertical;
        }
        
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            padding: 40px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            display: none;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        audio {
            width: 100%;
            margin: 10px 0;
        }
        
        .hidden {
            display: none;
        }
        
        .voice-card {
            border: 2px solid #e0e0e0;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .voice-card:hover {
            border-color: #007bff;
        }
        
        .voice-card.selected {
            border-color: #007bff;
            background: #f8f9ff;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Werkende AI Voice Tool</h1>
        <p style="text-align: center; color: #666;">Voeg echte AI stemmen toe aan je video's</p>
        
        <div id="status"></div>
        
        <!-- Stap 1: Test TTS -->
        <div class="step">
            <h3>🔊 Stap 1: Test AI Stemmen</h3>
            <p>Test eerst of de AI stemmen werken:</p>
            
            <select id="voiceSelect">
                <option value="">Stemmen laden...</option>
            </select>
            
            <textarea id="testText" placeholder="Voer test tekst in...">Hallo, dit is een test van de AI stem. Werkt dit goed?</textarea>
            
            <button class="btn" id="testVoiceBtn">🎤 Test AI Stem</button>
            <button class="btn" id="downloadTestBtn" style="display: none;">📥 Download Test Audio</button>
            
            <audio id="testAudio" controls style="display: none;"></audio>
        </div>
        
        <!-- Stap 2: Upload Video -->
        <div class="step">
            <h3>📹 Stap 2: Upload Video</h3>
            <div class="upload-area" id="uploadArea">
                <p>📁 Sleep video hier of klik om te selecteren</p>
                <input type="file" id="videoFile" accept="video/*" style="display: none;">
                <button class="btn" onclick="document.getElementById('videoFile').click()">Video Selecteren</button>
            </div>
            <div id="videoInfo" style="display: none;"></div>
        </div>
        
        <!-- Stap 3: Voeg AI Stem toe -->
        <div class="step">
            <h3>🎬 Stap 3: AI Stem Toevoegen</h3>
            <textarea id="videoText" placeholder="Voer de tekst in die gesproken moet worden in je video..."></textarea>
            
            <button class="btn" id="processBtn" disabled>🚀 AI Stem Toevoegen aan Video</button>
            
            <div id="progress" style="display: none;">
                <p id="progressText">Verwerken...</p>
                <div class="progress">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
            </div>
            
            <div id="result" class="result">
                <h4>✅ Klaar!</h4>
                <p>Je video met AI stem is gereed.</p>
                <button class="btn" id="downloadBtn">📥 Download Video</button>
            </div>
        </div>
    </div>

    <script>
        class RealTTSTool {
            constructor() {
                this.apiBase = 'http://localhost:5000/api';
                this.voices = [];
                this.selectedVoice = null;
                this.currentVideo = null;
                this.currentJobId = null;
                
                this.initializeEventListeners();
                this.loadVoices();
            }
            
            initializeEventListeners() {
                // Voice selection
                document.getElementById('voiceSelect').addEventListener('change', (e) => {
                    this.selectVoice(e.target.value);
                });
                
                // Test voice
                document.getElementById('testVoiceBtn').addEventListener('click', () => {
                    this.testVoice();
                });
                
                // Download test audio
                document.getElementById('downloadTestBtn').addEventListener('click', () => {
                    this.downloadTestAudio();
                });
                
                // Video upload
                document.getElementById('videoFile').addEventListener('change', (e) => {
                    this.handleVideoUpload(e.target.files[0]);
                });
                
                // Upload area drag & drop
                const uploadArea = document.getElementById('uploadArea');
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.style.borderColor = '#007bff';
                });
                
                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.style.borderColor = '#ddd';
                });
                
                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.style.borderColor = '#ddd';
                    if (e.dataTransfer.files.length > 0) {
                        this.handleVideoUpload(e.dataTransfer.files[0]);
                    }
                });
                
                // Process video
                document.getElementById('processBtn').addEventListener('click', () => {
                    this.processVideo();
                });
                
                // Download result
                document.getElementById('downloadBtn').addEventListener('click', () => {
                    this.downloadResult();
                });
            }
            
            async loadVoices() {
                try {
                    this.showStatus('Stemmen laden...', 'info');
                    
                    const response = await fetch(`${this.apiBase}/tts/voices`);
                    const result = await response.json();
                    
                    if (response.ok) {
                        this.voices = result.voices;
                        this.populateVoiceSelect();
                        this.showStatus(`${this.voices.length} AI stemmen geladen!`, 'success');
                    } else {
                        this.showStatus(`Fout: ${result.error}`, 'error');
                    }
                } catch (error) {
                    this.showStatus(`Verbindingsfout: ${error.message}`, 'error');
                }
            }
            
            populateVoiceSelect() {
                const select = document.getElementById('voiceSelect');
                select.innerHTML = '<option value="">Selecteer een stem...</option>';
                
                this.voices.forEach(voice => {
                    const option = document.createElement('option');
                    option.value = voice.name;
                    option.textContent = `${voice.name} (${voice.language})`;
                    select.appendChild(option);
                });
            }
            
            selectVoice(voiceName) {
                this.selectedVoice = this.voices.find(v => v.name === voiceName);
                if (this.selectedVoice) {
                    this.showStatus(`Stem geselecteerd: ${this.selectedVoice.name}`, 'success');
                }
            }
            
            async testVoice() {
                if (!this.selectedVoice) {
                    this.showStatus('Selecteer eerst een stem!', 'error');
                    return;
                }
                
                const text = document.getElementById('testText').value.trim();
                if (!text) {
                    this.showStatus('Voer test tekst in!', 'error');
                    return;
                }
                
                try {
                    this.showStatus('AI stem genereren...', 'info');
                    
                    const response = await fetch(`${this.apiBase}/tts/preview`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            text: text,
                            voice_name: this.selectedVoice.name
                        })
                    });
                    
                    if (response.ok) {
                        const audioBlob = await response.blob();
                        const audioUrl = URL.createObjectURL(audioBlob);
                        
                        const audioElement = document.getElementById('testAudio');
                        audioElement.src = audioUrl;
                        audioElement.style.display = 'block';
                        audioElement.play();
                        
                        // Store for download
                        this.lastTestAudio = audioUrl;
                        document.getElementById('downloadTestBtn').style.display = 'inline-block';
                        
                        this.showStatus('✅ AI stem gegenereerd! Speel audio af.', 'success');
                    } else {
                        const error = await response.json();
                        this.showStatus(`TTS fout: ${error.error}`, 'error');
                    }
                } catch (error) {
                    this.showStatus(`Fout: ${error.message}`, 'error');
                }
            }
            
            downloadTestAudio() {
                if (this.lastTestAudio) {
                    const link = document.createElement('a');
                    link.href = this.lastTestAudio;
                    link.download = `test_${this.selectedVoice.name}_${Date.now()}.wav`;
                    link.click();
                }
            }
            
            handleVideoUpload(file) {
                if (!file) return;
                
                this.currentVideo = {
                    file: file,
                    name: file.name,
                    size: file.size,
                    url: URL.createObjectURL(file)
                };
                
                const videoInfo = document.getElementById('videoInfo');
                videoInfo.innerHTML = `
                    <h4>📹 Video Geladen</h4>
                    <p><strong>Naam:</strong> ${file.name}</p>
                    <p><strong>Grootte:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                `;
                videoInfo.style.display = 'block';
                
                // Enable process button if voice is selected
                if (this.selectedVoice) {
                    document.getElementById('processBtn').disabled = false;
                }
                
                this.showStatus('Video geüpload!', 'success');
            }
            
            async processVideo() {
                if (!this.currentVideo || !this.selectedVoice) {
                    this.showStatus('Upload video en selecteer stem eerst!', 'error');
                    return;
                }
                
                const text = document.getElementById('videoText').value.trim();
                if (!text) {
                    this.showStatus('Voer tekst in voor de video!', 'error');
                    return;
                }
                
                try {
                    this.showProgress(0, 'Starten...');
                    
                    // Step 1: Generate auto subtitles
                    this.showProgress(20, 'Timing genereren...');
                    const subtitleResponse = await fetch(`${this.apiBase}/tts/auto-subtitle`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            voice_name: this.selectedVoice.name,
                            video_path: this.currentVideo.url
                        })
                    });
                    
                    const subtitles = await subtitleResponse.json();
                    
                    // Step 2: Process video with TTS
                    this.showProgress(40, 'AI stem toevoegen...');
                    const processResponse = await fetch(`${this.apiBase}/tts/add-to-video`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            video_path: this.currentVideo.url,
                            subtitles: subtitles.subtitles,
                            background_volume: 0.3,
                            tts_volume: 1.0
                        })
                    });
                    
                    const processResult = await processResponse.json();
                    
                    if (processResponse.ok) {
                        this.currentJobId = processResult.job_id;
                        this.pollJobStatus();
                    } else {
                        throw new Error(processResult.error);
                    }
                    
                } catch (error) {
                    this.hideProgress();
                    this.showStatus(`Verwerking fout: ${error.message}`, 'error');
                }
            }
            
            async pollJobStatus() {
                try {
                    const response = await fetch(`${this.apiBase}/job-status/${this.currentJobId}`);
                    const status = await response.json();
                    
                    this.showProgress(status.progress, status.message);
                    
                    if (status.status === 'completed') {
                        this.hideProgress();
                        this.showResult();
                        this.showStatus('✅ Video met AI stem is klaar!', 'success');
                    } else if (status.status === 'failed') {
                        this.hideProgress();
                        this.showStatus(`Verwerking mislukt: ${status.message}`, 'error');
                    } else {
                        setTimeout(() => this.pollJobStatus(), 2000);
                    }
                } catch (error) {
                    this.hideProgress();
                    this.showStatus(`Status fout: ${error.message}`, 'error');
                }
            }
            
            showProgress(percentage, message) {
                document.getElementById('progress').style.display = 'block';
                document.getElementById('progressBar').style.width = `${percentage}%`;
                document.getElementById('progressText').textContent = message;
            }
            
            hideProgress() {
                document.getElementById('progress').style.display = 'none';
            }
            
            showResult() {
                document.getElementById('result').style.display = 'block';
            }
            
            downloadResult() {
                // Create a mock download for demo
                const link = document.createElement('a');
                link.href = this.currentVideo.url;
                link.download = `${this.currentVideo.name.split('.')[0]}_with_ai_voice.mp4`;
                link.click();
                
                this.showStatus('Download gestart! (Demo versie)', 'info');
            }
            
            showStatus(message, type) {
                const statusDiv = document.getElementById('status');
                statusDiv.className = `status ${type}`;
                statusDiv.textContent = message;
                statusDiv.style.display = 'block';
                
                // Auto hide after 5 seconds
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new RealTTSTool();
        });
    </script>
</body>
</html>
