<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 Simple AI Voice + Video Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .step {
            background: rgba(255, 255, 255, 0.1);
            margin: 20px 0;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .step h3 {
            margin-top: 0;
            color: #ffd700;
            font-size: 1.3em;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn.success {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }
        
        select, textarea, input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 14px;
            margin: 10px 0;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.5);
            padding: 40px;
            text-align: center;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 10px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.success {
            background: rgba(0, 184, 148, 0.3);
            border: 1px solid #00b894;
        }
        
        .status.error {
            background: rgba(255, 107, 107, 0.3);
            border: 1px solid #ff6b6b;
        }
        
        .status.info {
            background: rgba(116, 185, 255, 0.3);
            border: 1px solid #74b9ff;
        }
        
        audio, video {
            width: 100%;
            margin: 15px 0;
            border-radius: 10px;
        }
        
        .progress {
            width: 100%;
            height: 25px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #00b894, #00cec9);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 15px;
        }
        
        .result-section {
            background: rgba(0, 184, 148, 0.2);
            border: 2px solid #00b894;
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
            display: none;
        }
        
        .timing-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .timing-box {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .timing-box h4 {
            margin-top: 0;
            color: #ffd700;
        }
        
        .speed-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .speed-btn {
            padding: 8px 15px;
            font-size: 12px;
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 AI Voice + Video Tool</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            Create videos with realistic AI voices - No FFmpeg required!
        </p>
        
        <div id="status"></div>
        
        <!-- Step 1: Voice Selection -->
        <div class="step">
            <h3>🗣️ Step 1: Choose AI Voice</h3>
            <select id="voiceSelect">
                <option value="">Loading voices...</option>
            </select>
            <textarea id="testText" placeholder="Enter test text...">Hello! This is a test of the AI voice system. The speech should be clear and natural sounding.</textarea>
            <button class="btn" id="testVoiceBtn">🎤 Test Voice</button>
            <button class="btn" id="downloadTestBtn" style="display: none;">📥 Download Test Audio</button>
            <audio id="testAudio" controls style="display: none;"></audio>
        </div>
        
        <!-- Step 2: Video Upload -->
        <div class="step">
            <h3>📹 Step 2: Upload Your Video</h3>
            <div class="upload-area" id="uploadArea">
                <p>📁 Drag video here or click to select</p>
                <input type="file" id="videoFile" accept="video/*" style="display: none;">
                <button class="btn" onclick="document.getElementById('videoFile').click()">Choose Video File</button>
            </div>
            <div id="videoInfo" style="display: none;"></div>
            <video id="videoPreview" controls style="display: none;"></video>
        </div>
        
        <!-- Step 3: Timing Settings -->
        <div class="step">
            <h3>⏱️ Step 3: Configure Timing</h3>
            <div class="timing-controls">
                <div class="timing-box">
                    <h4>Video Duration</h4>
                    <input type="number" id="videoDuration" value="60" min="5" max="600" step="5" placeholder="Seconds">
                    <button class="btn" id="detectDurationBtn">📹 Auto-Detect</button>
                </div>
                <div class="timing-box">
                    <h4>Speech Speed</h4>
                    <input type="range" id="speechSpeed" min="0.5" max="2.0" step="0.1" value="1.0">
                    <div id="speedValue">1.0x (Normal)</div>
                    <div class="speed-buttons">
                        <button class="btn speed-btn" onclick="setSpeechSpeed(0.8)">Slow</button>
                        <button class="btn speed-btn" onclick="setSpeechSpeed(1.0)">Normal</button>
                        <button class="btn speed-btn" onclick="setSpeechSpeed(1.3)">Fast</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Step 4: Add Voice -->
        <div class="step">
            <h3>🎬 Step 4: Add AI Voice</h3>
            <textarea id="videoText" placeholder="Enter the text that should be spoken in your video..."></textarea>
            <button class="btn" id="processBtn" disabled>🚀 Generate AI Voice Video</button>
            
            <div id="progress" style="display: none;">
                <p id="progressText">Processing...</p>
                <div class="progress">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
            </div>
        </div>
        
        <!-- Results -->
        <div class="result-section" id="resultSection">
            <h3>✅ Your AI Voice Video is Ready!</h3>
            <p>Your video has been processed with AI voice. Download the files below:</p>
            <button class="btn success" id="downloadVideoBtn">📥 Download Video</button>
            <button class="btn success" id="downloadAudioBtn">📥 Download Audio Track</button>
            <div id="resultPreview"></div>
        </div>
    </div>

    <script>
        class SimpleVideoAudioTool {
            constructor() {
                this.apiBase = 'http://localhost:5000/api';
                this.voices = [];
                this.selectedVoice = null;
                this.currentVideo = null;
                this.currentJobId = null;
                this.lastTestAudio = null;
                
                this.initializeEventListeners();
                this.loadVoices();
            }
            
            initializeEventListeners() {
                // Voice selection
                document.getElementById('voiceSelect').addEventListener('change', (e) => {
                    this.selectVoice(e.target.value);
                });
                
                // Test voice
                document.getElementById('testVoiceBtn').addEventListener('click', () => {
                    this.testVoice();
                });
                
                // Download test audio
                document.getElementById('downloadTestBtn').addEventListener('click', () => {
                    this.downloadTestAudio();
                });
                
                // Video upload
                document.getElementById('videoFile').addEventListener('change', (e) => {
                    this.handleVideoUpload(e.target.files[0]);
                });
                
                // Upload area drag & drop
                const uploadArea = document.getElementById('uploadArea');
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.style.borderColor = '#ffd700';
                });
                
                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.style.borderColor = 'rgba(255, 255, 255, 0.5)';
                });
                
                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.style.borderColor = 'rgba(255, 255, 255, 0.5)';
                    if (e.dataTransfer.files.length > 0) {
                        this.handleVideoUpload(e.dataTransfer.files[0]);
                    }
                });
                
                // Duration detection
                document.getElementById('detectDurationBtn').addEventListener('click', () => {
                    this.detectVideoDuration();
                });
                
                // Speech speed
                document.getElementById('speechSpeed').addEventListener('input', (e) => {
                    this.updateSpeedDisplay(e.target.value);
                });
                
                // Text input
                document.getElementById('videoText').addEventListener('input', () => {
                    this.checkProcessButtonState();
                });
                
                // Process button
                document.getElementById('processBtn').addEventListener('click', () => {
                    this.processVideo();
                });
                
                // Download buttons
                document.getElementById('downloadVideoBtn').addEventListener('click', () => {
                    this.downloadResult('video');
                });
                
                document.getElementById('downloadAudioBtn').addEventListener('click', () => {
                    this.downloadResult('audio');
                });
            }
            
            async loadVoices() {
                try {
                    this.showStatus('Loading AI voices...', 'info');
                    
                    const response = await fetch(`${this.apiBase}/tts/voices`);
                    const result = await response.json();
                    
                    if (response.ok) {
                        this.voices = result.voices;
                        this.populateVoiceSelect();
                        this.showStatus(`${this.voices.length} AI voices loaded!`, 'success');
                    } else {
                        this.showStatus(`Error: ${result.error}`, 'error');
                    }
                } catch (error) {
                    this.showStatus(`Connection error: ${error.message}`, 'error');
                }
            }
            
            populateVoiceSelect() {
                const select = document.getElementById('voiceSelect');
                select.innerHTML = '<option value="">Choose an AI voice...</option>';
                
                this.voices.forEach(voice => {
                    const option = document.createElement('option');
                    option.value = voice.name;
                    option.textContent = `${voice.name} (${voice.description || voice.language})`;
                    select.appendChild(option);
                });
            }
            
            selectVoice(voiceName) {
                this.selectedVoice = this.voices.find(v => v.name === voiceName);
                if (this.selectedVoice) {
                    this.showStatus(`Voice selected: ${this.selectedVoice.name}`, 'success');
                    this.checkProcessButtonState();
                }
            }
            
            async testVoice() {
                if (!this.selectedVoice) {
                    this.showStatus('Please select a voice first!', 'error');
                    return;
                }
                
                const text = document.getElementById('testText').value.trim();
                if (!text) {
                    this.showStatus('Please enter test text!', 'error');
                    return;
                }
                
                try {
                    this.showStatus('Generating AI voice...', 'info');
                    
                    const response = await fetch(`${this.apiBase}/tts/preview`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            voice_name: this.selectedVoice.name
                        })
                    });
                    
                    if (response.ok) {
                        const audioBlob = await response.blob();
                        const audioUrl = URL.createObjectURL(audioBlob);
                        
                        const audioElement = document.getElementById('testAudio');
                        audioElement.src = audioUrl;
                        audioElement.style.display = 'block';
                        audioElement.play();
                        
                        this.lastTestAudio = audioUrl;
                        document.getElementById('downloadTestBtn').style.display = 'inline-block';
                        
                        this.showStatus('✅ AI voice generated! Play audio above.', 'success');
                    } else {
                        const error = await response.json();
                        this.showStatus(`TTS error: ${error.error}`, 'error');
                    }
                } catch (error) {
                    this.showStatus(`Error: ${error.message}`, 'error');
                }
            }
            
            downloadTestAudio() {
                if (this.lastTestAudio) {
                    const link = document.createElement('a');
                    link.href = this.lastTestAudio;
                    link.download = `test_${this.selectedVoice.name}_${Date.now()}.wav`;
                    link.click();
                }
            }
            
            handleVideoUpload(file) {
                if (!file) return;
                
                this.currentVideo = {
                    file: file,
                    name: file.name,
                    size: file.size,
                    url: URL.createObjectURL(file)
                };
                
                const videoInfo = document.getElementById('videoInfo');
                videoInfo.innerHTML = `
                    <h4>📹 Video Loaded</h4>
                    <p><strong>Name:</strong> ${file.name}</p>
                    <p><strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>Type:</strong> ${file.type}</p>
                `;
                videoInfo.style.display = 'block';
                
                // Show video preview
                const videoPreview = document.getElementById('videoPreview');
                videoPreview.src = this.currentVideo.url;
                videoPreview.style.display = 'block';
                
                // Try to detect duration
                this.detectVideoDuration();
                this.checkProcessButtonState();
                
                this.showStatus('Video uploaded successfully!', 'success');
            }
            
            detectVideoDuration() {
                if (!this.currentVideo) {
                    this.showStatus('Upload a video first!', 'error');
                    return;
                }
                
                const video = document.createElement('video');
                video.src = this.currentVideo.url;
                
                video.addEventListener('loadedmetadata', () => {
                    const duration = Math.round(video.duration);
                    if (duration && duration > 0) {
                        document.getElementById('videoDuration').value = duration;
                        this.showStatus(`Video duration detected: ${duration} seconds`, 'success');
                    } else {
                        this.showStatus('Could not detect duration - enter manually', 'error');
                    }
                });
                
                video.addEventListener('error', () => {
                    this.showStatus('Could not detect duration - enter manually', 'error');
                });
            }
            
            updateSpeedDisplay(speed) {
                const speedValue = document.getElementById('speedValue');
                const speedFloat = parseFloat(speed);
                
                let description = '';
                if (speedFloat < 0.8) description = ' (Very slow)';
                else if (speedFloat < 1.0) description = ' (Slow)';
                else if (speedFloat === 1.0) description = ' (Normal)';
                else if (speedFloat <= 1.3) description = ' (Fast)';
                else description = ' (Very fast)';
                
                speedValue.textContent = `${speedFloat}x${description}`;
            }
            
            checkProcessButtonState() {
                const processBtn = document.getElementById('processBtn');
                const hasVoice = this.selectedVoice !== null;
                const hasVideo = this.currentVideo !== null;
                const hasText = document.getElementById('videoText').value.trim().length > 0;
                
                if (hasVoice && hasVideo && hasText) {
                    processBtn.disabled = false;
                    processBtn.classList.add('success');
                    this.showStatus('✅ Ready to process!', 'success');
                } else {
                    processBtn.disabled = true;
                    processBtn.classList.remove('success');
                    
                    let missing = [];
                    if (!hasVoice) missing.push('voice');
                    if (!hasVideo) missing.push('video');
                    if (!hasText) missing.push('text');
                    
                    this.showStatus(`Still needed: ${missing.join(', ')}`, 'info');
                }
            }
            
            async processVideo() {
                if (!this.currentVideo || !this.selectedVoice) {
                    this.showStatus('Upload video and select voice first!', 'error');
                    return;
                }
                
                const text = document.getElementById('videoText').value.trim();
                if (!text) {
                    this.showStatus('Enter text for the video!', 'error');
                    return;
                }
                
                try {
                    this.showProgress(0, 'Starting...');
                    
                    const videoDuration = parseInt(document.getElementById('videoDuration').value) || 60;
                    const speechSpeed = parseFloat(document.getElementById('speechSpeed').value) || 1.0;
                    
                    // Generate subtitles
                    this.showProgress(20, `Generating timing for ${videoDuration} seconds...`);
                    const subtitleResponse = await fetch(`${this.apiBase}/tts/auto-subtitle`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            voice_name: this.selectedVoice.name,
                            video_duration: videoDuration,
                            speech_speed: speechSpeed
                        })
                    });
                    
                    const subtitles = await subtitleResponse.json();
                    
                    // Process video
                    this.showProgress(40, 'Adding AI voice...');
                    const processResponse = await fetch(`${this.apiBase}/tts/add-to-video`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            video_path: this.currentVideo.url,
                            subtitles: subtitles.subtitles,
                            background_volume: 0.3,
                            tts_volume: 1.0
                        })
                    });
                    
                    const processResult = await processResponse.json();
                    
                    if (processResponse.ok) {
                        this.currentJobId = processResult.job_id;
                        this.pollJobStatus();
                    } else {
                        throw new Error(processResult.error);
                    }
                    
                } catch (error) {
                    this.hideProgress();
                    this.showStatus(`Processing error: ${error.message}`, 'error');
                }
            }
            
            async pollJobStatus() {
                try {
                    const response = await fetch(`${this.apiBase}/job-status/${this.currentJobId}`);
                    const status = await response.json();
                    
                    this.showProgress(status.progress, status.message);
                    
                    if (status.status === 'completed') {
                        this.hideProgress();
                        this.showResult();
                        this.showStatus('✅ AI voice video is ready!', 'success');
                    } else if (status.status === 'failed') {
                        this.hideProgress();
                        this.showStatus(`Processing failed: ${status.message}`, 'error');
                    } else {
                        setTimeout(() => this.pollJobStatus(), 2000);
                    }
                } catch (error) {
                    this.hideProgress();
                    this.showStatus(`Status error: ${error.message}`, 'error');
                }
            }
            
            showProgress(percentage, message) {
                document.getElementById('progress').style.display = 'block';
                document.getElementById('progressBar').style.width = `${percentage}%`;
                document.getElementById('progressText').textContent = message;
            }
            
            hideProgress() {
                document.getElementById('progress').style.display = 'none';
            }
            
            showResult() {
                document.getElementById('resultSection').style.display = 'block';
                
                // Scroll to result
                document.getElementById('resultSection').scrollIntoView({ 
                    behavior: 'smooth' 
                });
            }
            
            downloadResult(type) {
                if (!this.currentJobId) return;
                
                const url = `${this.apiBase}/download/${this.currentJobId}`;
                const link = document.createElement('a');
                link.href = url;
                link.download = `ai_voice_${type}_${this.currentJobId}.${type === 'video' ? 'mp4' : 'wav'}`;
                link.click();
                
                this.showStatus(`Downloading ${type}...`, 'info');
            }
            
            showStatus(message, type) {
                const statusDiv = document.getElementById('status');
                statusDiv.className = `status ${type}`;
                statusDiv.textContent = message;
                statusDiv.style.display = 'block';
                
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }
        
        // Global function for speed buttons
        function setSpeechSpeed(speed) {
            document.getElementById('speechSpeed').value = speed;
            const event = new Event('input');
            document.getElementById('speechSpeed').dispatchEvent(event);
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleVideoAudioTool();
        });
    </script>
</body>
</html>
