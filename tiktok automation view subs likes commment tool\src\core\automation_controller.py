"""
Automation Controller for TikTok Automation Tool
Coordinates between UI and automation engine with advanced features.
"""

import time
import logging
from typing import Dict, List, Optional, Callable
from pathlib import Path
from threading import Thread, Event
from queue import Queue
import json

from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QMessageBox

from core.automation_engine import AutomationEngine, AutomationTask, ActionType
from core.proxy_manager import ProxyManager
from core.captcha_solver import CaptchaSolver
from utils.logger import AutomationLogger, PerformanceLogger
from utils.config import Config

class AutomationController(QObject):
    """Controls automation operations with advanced features."""
    
    # Signals for UI updates
    progress_updated = pyqtSignal(str, int)  # action_type, progress
    status_updated = pyqtSignal(str)  # status_message
    statistics_updated = pyqtSignal(dict)  # statistics
    automation_finished = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, config: Config, parent=None):
        """Initialize automation controller.
        
        Args:
            config: Configuration manager
            parent: Parent QObject
        """
        super().__init__(parent)
        
        self.config = config
        self.automation_engine = None
        self.proxy_manager = None
        self.captcha_solver = None
        
        # Logging
        log_dir = Path(__file__).parent.parent.parent / "logs"
        self.automation_logger = AutomationLogger(log_dir)
        self.performance_logger = PerformanceLogger(log_dir)
        
        # State management
        self.is_running = False
        self.is_paused = False
        self.current_tasks = []
        self.completed_tasks = 0
        self.failed_tasks = 0
        
        # Performance monitoring
        self.start_time = None
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self._monitor_performance)
        
        # Initialize components
        self._init_components()
        
        logging.info("Automation controller initialized")
    
    def _init_components(self):
        """Initialize automation components."""
        # Get configuration
        automation_config = {
            'headless_mode': self.config.get('automation', 'headless_mode', False),
            'use_proxy': self.config.get('automation', 'use_proxy', False),
            'default_delay': self.config.get('automation', 'default_delay', 2),
            'max_retries': self.config.get('automation', 'max_retries', 3),
            'timeout': self.config.get('automation', 'timeout', 30)
        }
        
        # Initialize proxy manager
        proxy_config = {
            'enabled': self.config.get('automation', 'use_proxy', False),
            'host': self.config.get_automation_setting('proxy_host', ''),
            'port': self.config.get_automation_setting('proxy_port', 8080),
            'type': self.config.get_automation_setting('proxy_type', 'HTTP'),
            'username': self.config.get_automation_setting('proxy_username', ''),
            'password': self.config.get_automation_setting('proxy_password', '')
        }
        self.proxy_manager = ProxyManager(proxy_config)
        
        # Initialize captcha solver
        captcha_config = {
            'service': self.config.get_automation_setting('captcha_service', 'none'),
            'api_key': self.config.get_automation_setting('captcha_api_key', ''),
            'timeout': self.config.get('automation', 'timeout', 120)
        }
        self.captcha_solver = CaptchaSolver(captcha_config)
        
        # Initialize automation engine
        self.automation_engine = AutomationEngine(automation_config, self.automation_logger)
        self.automation_engine.set_callbacks(
            progress_callback=self._on_progress_update,
            status_callback=self._on_status_update
        )
    
    def add_automation_task(self, target_url: str, actions: Dict[str, int], custom_comments: List[str] = None):
        """Add automation task.
        
        Args:
            target_url: Target TikTok URL or username
            actions: Dictionary of actions and quantities
            custom_comments: List of custom comments
        """
        tasks = []
        
        # Create tasks for each action
        for action_name, quantity in actions.items():
            if quantity > 0:
                action_type = ActionType(action_name.lower())
                
                custom_data = {}
                if action_type == ActionType.COMMENT and custom_comments:
                    custom_data['comments'] = custom_comments
                
                task = AutomationTask(
                    action_type=action_type,
                    target_url=target_url,
                    quantity=quantity,
                    custom_data=custom_data if custom_data else None
                )
                
                tasks.append(task)
                self.automation_engine.add_task(task)
        
        self.current_tasks.extend(tasks)
        
        logging.info(f"Added {len(tasks)} automation tasks for: {target_url}")
        self.status_updated.emit(f"Added {len(tasks)} tasks for {target_url}")
    
    def start_automation(self) -> bool:
        """Start automation process.
        
        Returns:
            True if started successfully
        """
        if self.is_running:
            logging.warning("Automation already running")
            return False
        
        if not self.current_tasks:
            logging.warning("No tasks to execute")
            self.status_updated.emit("No tasks to execute")
            return False
        
        # Reset statistics
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.start_time = time.time()
        
        # Start automation engine
        if self.automation_engine.start_automation():
            self.is_running = True
            self.is_paused = False
            
            # Start performance monitoring
            self.performance_timer.start(5000)  # Update every 5 seconds
            
            self.status_updated.emit("🚀 Automation started")
            self.automation_logger.log_session_start("automation", len(self.current_tasks))
            
            # Start monitoring thread
            monitor_thread = Thread(target=self._monitor_automation, daemon=True)
            monitor_thread.start()
            
            return True
        
        return False
    
    def stop_automation(self):
        """Stop automation process."""
        if not self.is_running:
            return
        
        self.automation_engine.stop_automation()
        self.is_running = False
        self.is_paused = False
        
        # Stop performance monitoring
        self.performance_timer.stop()
        
        # Calculate duration
        duration = time.time() - self.start_time if self.start_time else 0
        
        # Log session end
        self.automation_logger.log_session_end(
            "automation",
            self.completed_tasks,
            self.failed_tasks,
            duration
        )
        
        self.status_updated.emit("⏹️ Automation stopped")
        self.automation_finished.emit(True, "Automation stopped by user")
        
        logging.info("Automation stopped")
    
    def pause_automation(self):
        """Pause automation process."""
        if self.is_running and not self.is_paused:
            self.automation_engine.pause_automation()
            self.is_paused = True
            self.status_updated.emit("⏸️ Automation paused")
    
    def resume_automation(self):
        """Resume automation process."""
        if self.is_running and self.is_paused:
            self.automation_engine.resume_automation()
            self.is_paused = False
            self.status_updated.emit("▶️ Automation resumed")
    
    def clear_tasks(self):
        """Clear all tasks."""
        if self.is_running:
            logging.warning("Cannot clear tasks while automation is running")
            return
        
        self.current_tasks.clear()
        self.completed_tasks = 0
        self.failed_tasks = 0
        
        self.status_updated.emit("📋 Tasks cleared")
        logging.info("All tasks cleared")
    
    def get_statistics(self) -> Dict:
        """Get current automation statistics.
        
        Returns:
            Dictionary containing statistics
        """
        base_stats = {
            'total_tasks': len(self.current_tasks),
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'success_rate': 0,
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'duration': 0
        }
        
        # Calculate success rate
        if self.completed_tasks + self.failed_tasks > 0:
            base_stats['success_rate'] = (self.completed_tasks / (self.completed_tasks + self.failed_tasks)) * 100
        
        # Calculate duration
        if self.start_time:
            base_stats['duration'] = time.time() - self.start_time
        
        # Get engine statistics if available
        if self.automation_engine:
            engine_stats = self.automation_engine.get_statistics()
            base_stats.update(engine_stats)
        
        return base_stats
    
    def _monitor_automation(self):
        """Monitor automation progress in separate thread."""
        while self.is_running:
            try:
                # Update statistics
                stats = self.get_statistics()
                self.statistics_updated.emit(stats)
                
                # Check if automation completed
                if (self.automation_engine and 
                    not self.automation_engine.is_running and 
                    self.automation_engine.task_queue.empty()):
                    
                    self.is_running = False
                    self.performance_timer.stop()
                    
                    # Calculate final statistics
                    duration = time.time() - self.start_time if self.start_time else 0
                    success_rate = stats.get('success_rate', 0)
                    
                    message = f"Automation completed! Success rate: {success_rate:.1f}%"
                    self.automation_finished.emit(True, message)
                    
                    break
                
                time.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                logging.error(f"Automation monitoring error: {e}")
                break
    
    def _monitor_performance(self):
        """Monitor system performance."""
        try:
            import psutil
            
            # Get system metrics
            cpu_percent = psutil.cpu_percent()
            memory_info = psutil.virtual_memory()
            memory_mb = memory_info.used / 1024 / 1024
            
            # Get process metrics
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024
            thread_count = process.num_threads()
            
            # Log performance metrics
            self.performance_logger.log_resource_usage(cpu_percent, process_memory, thread_count)
            
            # Emit performance update (could be used by UI)
            performance_data = {
                'cpu_percent': cpu_percent,
                'memory_mb': process_memory,
                'threads': thread_count,
                'system_memory_mb': memory_mb
            }
            
            # Update status bar or performance widget if needed
            
        except ImportError:
            # psutil not available, skip performance monitoring
            pass
        except Exception as e:
            logging.error(f"Performance monitoring error: {e}")
    
    def _on_progress_update(self, action_type: str, progress: int):
        """Handle progress update from automation engine."""
        self.progress_updated.emit(action_type, progress)
    
    def _on_status_update(self, status: str):
        """Handle status update from automation engine."""
        self.status_updated.emit(status)
    
    def update_proxy_settings(self, proxy_config: Dict):
        """Update proxy settings.
        
        Args:
            proxy_config: New proxy configuration
        """
        if self.proxy_manager:
            # Update proxy manager configuration
            self.proxy_manager.config.update(proxy_config)
            
            # Save to config
            for key, value in proxy_config.items():
                self.config.set_automation_setting(f'proxy_{key}', value)
            
            self.config.save()
            
            logging.info("Proxy settings updated")
            self.status_updated.emit("Proxy settings updated")
    
    def update_captcha_settings(self, captcha_config: Dict):
        """Update captcha settings.
        
        Args:
            captcha_config: New captcha configuration
        """
        if self.captcha_solver:
            # Update captcha solver configuration
            self.captcha_solver.config.update(captcha_config)
            
            # Save to config
            for key, value in captcha_config.items():
                self.config.set_automation_setting(f'captcha_{key}', value)
            
            self.config.save()
            
            logging.info("Captcha settings updated")
            self.status_updated.emit("Captcha settings updated")
    
    def test_proxy_connection(self) -> bool:
        """Test proxy connection.
        
        Returns:
            True if proxy is working
        """
        if not self.proxy_manager or not self.proxy_manager.is_enabled():
            return True  # No proxy configured
        
        current_proxy = self.proxy_manager.get_current_proxy()
        if current_proxy:
            return self.proxy_manager.test_proxy(current_proxy)
        
        return False
    
    def export_session_data(self, file_path: str):
        """Export session data to file.
        
        Args:
            file_path: Path to export file
        """
        try:
            session_data = {
                'statistics': self.get_statistics(),
                'tasks': [
                    {
                        'action_type': task.action_type.value,
                        'target_url': task.target_url,
                        'quantity': task.quantity,
                        'custom_data': task.custom_data
                    }
                    for task in self.current_tasks
                ],
                'results': [
                    {
                        'action_type': result.action_type.value,
                        'target_url': result.target_url,
                        'success': result.success,
                        'message': result.message,
                        'timestamp': result.timestamp
                    }
                    for result in (self.automation_engine.results if self.automation_engine else [])
                ],
                'export_time': time.time()
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Session data exported to: {file_path}")
            self.status_updated.emit(f"Session data exported to: {file_path}")
            
        except Exception as e:
            logging.error(f"Failed to export session data: {e}")
            self.status_updated.emit(f"Export failed: {e}")
