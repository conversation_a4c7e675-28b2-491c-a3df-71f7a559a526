from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, Default, <PERSON>ont<PERSON>vents, Font, <PERSON><PERSON>TNAME, Picture,
    OLE_XSIZE_CONTAINER, FON<PERSON>NDERSCORE, typelib_path,
    OLE_YSIZE_HIMETRIC, DISPMETHOD, IEnumVARIANT, EXCEPINFO,
    OLE_YSIZE_CONTAINER, DISPPARAMS, _check_version,
    FON<PERSON><PERSON><PERSON>KETHROUGH, OLE_OPTEXCLUSIVE, O<PERSON>_E<PERSON><PERSON>EDEFAULTBOOL,
    StdFont, FONTBOLD, OLE_XSIZE_HIMETRIC, IPictureDisp,
    OLE_XPOS_HIMETRIC, OLE_HANDLE, VgaColor, OLE_CANCELBOOL, CoClass,
    Checked, _lcid, <PERSON>ISPPROPERTY, IFontEventsDisp, IPicture,
    <PERSON><PERSON>_YPOS_CONTAINER, OLE_XSIZE_PIXELS, <PERSON><PERSON>patch,
    <PERSON>LE_YPOS_HIMETRIC, IFontDisp, IFont, VARIANT_BOOL, BSTR,
    FONTITALIC, Unchecked, OLE_YPOS_PIXELS, OLE_XPOS_PIXELS, HRESULT,
    dispid, OLE_COLOR, FONTSIZE, StdPicture, IUnknown, Gray,
    OLE_XPOS_CONTAINER, GUID, OLE_YSIZE_PIXELS, Color, Library,
    Monochrome
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'VgaColor', 'Default', 'FontEvents', 'OLE_CANCELBOOL', 'Font',
    'FONTNAME', 'Picture', 'OLE_XSIZE_CONTAINER', 'Checked',
    'IFontEventsDisp', 'IPicture', 'OLE_YPOS_CONTAINER',
    'OLE_TRISTATE', 'FONTUNDERSCORE', 'OLE_XSIZE_PIXELS',
    'typelib_path', 'OLE_YSIZE_HIMETRIC', 'OLE_YPOS_HIMETRIC',
    'IFontDisp', 'IFont', 'Monochrome', 'FONTITALIC', 'Unchecked',
    'OLE_YSIZE_CONTAINER', 'OLE_XPOS_PIXELS', 'OLE_YPOS_PIXELS',
    'OLE_COLOR', 'FONTSTRIKETHROUGH', 'FONTSIZE', 'StdPicture',
    'OLE_OPTEXCLUSIVE', 'OLE_ENABLEDEFAULTBOOL', 'StdFont', 'Gray',
    'FONTBOLD', 'OLE_XPOS_CONTAINER', 'OLE_XSIZE_HIMETRIC',
    'LoadPictureConstants', 'IPictureDisp', 'OLE_YSIZE_PIXELS',
    'Color', 'Library', 'OLE_XPOS_HIMETRIC', 'OLE_HANDLE'
]

