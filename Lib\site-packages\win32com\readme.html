<!DOCTYPE html>
<html lang="en">
<head>
  <title>win32com Readme</title>
</head>
<body>

<p><img width="551" height="99" id="_x0000_i1025"
 src="html%5Cimage%5Cpycom_blowing.gif"
 alt="Python and COM - Blowing the others away"> </p>

<h1>Python COM Extensions Readme </h1>

<p>This is the readme for win32com. Please check out the <a
 href="html/docindex.html">win32com documentation index</a></p>

<p>The <a href="test/.">win32com/test directory</a> contains some interesting
 scripts (and a new <a href="test/readme.txt">readme.txt</a>). Although these
 are used for testing, they do show a variety of COM techniques.</p>

<h3>VARIANT objects</h3>
<p>win32com.client now has explicit VARIANT objects which can be used in
situations where you need more control over the argument types passed when
calling COM methods.  See the <a href="html/variant.html">documentation on
this object</a>

<a name="currency"><h3>Important Currency changes</h3></a>
<p>
In all builds prior to 204, a COM currency value was returned as a tuple of
integers.  Working with 2 integers to represent a currency object was a poor
choice, but the alternative was never clear.  Now Python ships with the
<a href="https://docs.python.org/dev/library/decimal.html">decimal</a>
module, the alternative has arrived!
</p>
<p>
Up until build 212, code could set <code>pythoncom.__future_currency__ = True</code>
to force use of the decimal module, with a warning issued otherwise.  In
builds 213 and later, the decimal module is unconditionally used when
pythoncom returns you a currency value.
</p>

<h3>Recent Changes</h3>

<h4>Lots of internal changes on the road to py3k</h4>

<h4>win32com.axcontrol and win2con.internet</h4>
Many more interfaces for hosting AX controls and the interfaces
used by Internet Explorer.

<h4>win32com.shell</h4>
The shell interfaces have undergone a number of enhancements and changes.
A couple of methods have changed signature between the first build with shell support (200) and later builds.
SHGetFileInfo was broken in its result handling, so had to be changed - this
is the only function used by the samples that changed, but others not used by the samples also have changed.
These shell interfaces are now generally stable.
<h4>New win32com.taskscheduler module</h4>
Roger Upole has contributed an interface to the Windows task scheduler.  This is actually very neat, and it allows
Python to edit the task list as shown by Windows Control Panel.  Property page suppport may even appear later,
now that the win32 library has the new win32rcparser module.
<h4>ActiveX Scripting </h4>

<p>Python only supports "trusted" execution hosts - thus, it will no longer work
as an engine inside IE (Python itself no longer has a restricted execution environment).
Python continues to work fine as an Active Scripting Engine in all other
applications, including Windows Scripting Host, and ASP.

<p>There is also support for Python as an ActiveX Scripting Host.</p>

<p>Active Debugging seems to be fully functional.</p>

<h4>Older stuff</h4>
<ul>
</li>
  <li>Unexpected exceptions in Python COM objects will generally now dump
the exception and traceback to stdout. &nbsp;This is useful for debugging
and testing - it means that in some cases there will be no need to register
an object with <span style="font-style: italic;">--debug</span> to see these
tracebacks. &nbsp;Note that COM objects used by server processes (such as
ASP) generally have no valid stdout, so will still need to use <span
 style="font-style: italic;">--debug</span> as usual.<br>
  </li>
   <li>universal gateway support has been improved - we can now work as an
Outlook Addin<br>
  </li>

</body>
</html>
