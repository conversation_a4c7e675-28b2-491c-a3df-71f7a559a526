#!/usr/bin/env python3
"""
Working Video Processor - Actually combines TTS audio with video
Uses MoviePy for real video + audio integration
"""

import os
import tempfile
import time
from pathlib import Path

def create_video_with_audio(audio_files, output_path, video_duration=60, video_size=(640, 480)):
    """
    Create a video with TTS audio integrated
    
    Args:
        audio_files: List of audio file paths with timing
        output_path: Path for output video
        video_duration: Duration of video in seconds
        video_size: Video dimensions (width, height)
    """
    try:
        # Import MoviePy components
        from moviepy.editor import ColorClip, AudioFileClip, CompositeAudioClip, concatenate_audioclips
        
        print(f"🎬 Creating video with {len(audio_files)} audio segments")
        
        # Create a simple colored background video
        video_clip = ColorClip(
            size=video_size, 
            color=(50, 100, 150),  # Nice blue color
            duration=video_duration
        )
        
        # Collect audio clips
        audio_clips = []
        
        for i, audio_info in enumerate(audio_files):
            audio_path = audio_info.get('file')
            start_time = audio_info.get('start_time', 0)
            
            if not os.path.exists(audio_path):
                print(f"⚠️ Audio file not found: {audio_path}")
                continue
            
            try:
                # Load audio clip
                audio_clip = AudioFileClip(audio_path)
                
                # Set start time
                audio_clip = audio_clip.set_start(start_time)
                
                audio_clips.append(audio_clip)
                print(f"🎤 Added audio {i+1}: {start_time:.1f}s - {audio_info.get('text', '')[:30]}...")
                
            except Exception as e:
                print(f"⚠️ Error loading audio {i+1}: {e}")
                continue
        
        if not audio_clips:
            print("❌ No valid audio clips found")
            return False
        
        # Combine all audio clips
        final_audio = CompositeAudioClip(audio_clips)
        
        # Set the audio to the video
        final_video = video_clip.set_audio(final_audio)
        
        # Write the final video
        print(f"💾 Writing video to: {output_path}")
        final_video.write_videofile(
            output_path,
            fps=24,
            codec='libx264',
            audio_codec='aac',
            verbose=False,
            logger=None
        )
        
        # Clean up
        video_clip.close()
        final_audio.close()
        final_video.close()
        for clip in audio_clips:
            clip.close()
        
        # Check if file was created successfully
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ Video created successfully!")
            print(f"📊 File size: {file_size / 1024 / 1024:.1f} MB")
            return True
        else:
            print("❌ Video file was not created")
            return False
            
    except ImportError:
        print("❌ MoviePy not available - cannot create video")
        return False
    except Exception as e:
        print(f"❌ Error creating video: {e}")
        return False

def create_demo_video_with_tts(tts_files, output_path, duration=60):
    """
    Create a demo video with TTS audio
    
    Args:
        tts_files: List of TTS audio files with timing info
        output_path: Output video path
        duration: Video duration in seconds
    """
    
    # Prepare audio segments
    audio_segments = []
    for i, tts_file in enumerate(tts_files):
        if isinstance(tts_file, str):
            # Simple file path
            audio_segments.append({
                'file': tts_file,
                'start_time': i * (duration / len(tts_files)),
                'text': f'TTS segment {i+1}'
            })
        elif isinstance(tts_file, dict):
            # Already has timing info
            audio_segments.append(tts_file)
    
    return create_video_with_audio(
        audio_files=audio_segments,
        output_path=output_path,
        video_duration=duration
    )

def test_video_creation():
    """Test video creation with fake audio files"""
    print("🧪 Testing Video Creation")
    
    # Create some fake audio file paths for testing
    fake_audio_files = [
        {
            'file': 'test_audio_1.wav',
            'start_time': 0,
            'text': 'First audio segment'
        },
        {
            'file': 'test_audio_2.wav', 
            'start_time': 5,
            'text': 'Second audio segment'
        }
    ]
    
    output_path = os.path.join(tempfile.gettempdir(), "test_output.mp4")
    
    # This will fail because audio files don't exist, but will test the video creation logic
    success = create_video_with_audio(fake_audio_files, output_path, video_duration=10)
    
    if success:
        print("✅ Test passed!")
    else:
        print("⚠️ Test failed (expected due to missing audio files)")
    
    return success

if __name__ == "__main__":
    test_video_creation()
