#!/usr/bin/env python3
"""
TikTok Automation Tool - Main Application Entry Point
Professional TikTok automation tool for educational purposes only.

Author: AI Assistant
Version: 1.0.0
License: Educational Use Only

⚠️ LEGAL DISCLAIMER:
This tool is for educational purposes only. Users must comply with:
- TikTok's Terms of Service
- Local and international laws
- Platform guidelines and policies
Use at your own risk and responsibility.
"""

import sys
import os
import logging
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog
from PyQt5.QtCore import Qt, QDir
from PyQt5.QtGui import QIcon, QFont

# Add src directory to Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ui.main_window import MainWindow
from ui.login_dialog import LoginDialog
from ui.splash_screen import SplashScreen
from utils.logger import setup_logging
from utils.config import Config
from utils.theme import ThemeManager
from utils.security import SecurityManager, AuthenticationManager
from utils.error_handler import setup_global_error_handler

class TikTokAutomationApp:
    """Main application class for TikTok Automation Tool."""
    
    def __init__(self):
        """Initialize the application."""
        self.app = None
        self.main_window = None
        self.config = None
        self.theme_manager = None
        self.security_manager = None
        self.auth_manager = None
        self.current_user = None
        self.splash_screen = None
        
    def setup_application(self):
        """Setup the QApplication with proper configuration."""
        # Create QApplication
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("TikTok Automation Tool")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("TikTok Automation")
        
        # Set application icon
        icon_path = Path(__file__).parent / "assets" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
        
        # Set application font
        font = QFont("Segoe UI", 9)
        self.app.setFont(font)
        
        # Enable high DPI scaling
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        # Setup global error handler
        log_dir = Path(__file__).parent / "logs"
        setup_global_error_handler(self.app, log_dir)
        
    def setup_logging(self):
        """Setup application logging."""
        log_dir = Path(__file__).parent / "logs"
        log_dir.mkdir(exist_ok=True)
        setup_logging(log_dir)
        logging.info("TikTok Automation Tool started")
        
    def setup_config(self):
        """Setup application configuration."""
        config_dir = Path(__file__).parent / "config"
        config_dir.mkdir(exist_ok=True)
        self.config = Config(config_dir)
        
    def setup_theme(self):
        """Setup theme manager."""
        self.theme_manager = ThemeManager(self.app)

        # Load saved theme or default to dark
        theme = self.config.get('ui', 'theme', 'dark')
        self.theme_manager.set_theme(theme)

    def setup_security(self):
        """Setup security and authentication."""
        config_dir = Path(__file__).parent / "config"
        self.security_manager = SecurityManager(config_dir)
        self.auth_manager = AuthenticationManager(self.security_manager)
        
    def show_legal_disclaimer(self):
        """Show legal disclaimer dialog."""
        if not self.config.get('app', 'disclaimer_accepted', False):
            msg = QMessageBox()
            msg.setWindowTitle("Legal Disclaimer")
            msg.setIcon(QMessageBox.Warning)
            msg.setText(
                "⚠️ IMPORTANT LEGAL DISCLAIMER\n\n"
                "This TikTok Automation Tool is for EDUCATIONAL PURPOSES ONLY.\n\n"
                "By using this software, you acknowledge that:\n"
                "• You will comply with TikTok's Terms of Service\n"
                "• You will follow all applicable laws and regulations\n"
                "• You use this tool at your own risk and responsibility\n"
                "• The developers are not liable for any misuse\n\n"
                "Do you accept these terms and conditions?"
            )
            msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg.setDefaultButton(QMessageBox.No)
            
            if msg.exec_() == QMessageBox.Yes:
                self.config.set('app', 'disclaimer_accepted', True)
                self.config.save()
            else:
                sys.exit(0)
    
    def show_authentication(self) -> bool:
        """Show authentication dialog.

        Returns:
            True if authentication successful or guest mode
        """
        # Check if authentication is required
        require_auth = self.config.get('security', 'require_authentication', True)

        if not require_auth:
            self.current_user = "guest"
            return True

        # Show login dialog
        login_dialog = LoginDialog(self.auth_manager)
        login_dialog.login_successful.connect(self._on_login_successful)

        result = login_dialog.exec_()
        return result == QDialog.Accepted

    def _on_login_successful(self, username: str):
        """Handle successful login."""
        self.current_user = username
        logging.info(f"User authenticated: {username}")

    def create_main_window(self):
        """Create and setup the main window."""
        self.main_window = MainWindow(
            self.config,
            self.theme_manager,
            self.auth_manager,
            self.current_user
        )
        self.main_window.show()
        
    def show_splash_screen(self):
        """Show splash screen during initialization."""
        self.splash_screen = SplashScreen()
        self.splash_screen.show()
        self.splash_screen.start_loading()

        # Process events to show splash screen
        self.app.processEvents()

        return self.splash_screen

    def run(self):
        """Run the application."""
        try:
            # Setup application components
            self.setup_application()

            # Show splash screen
            splash = self.show_splash_screen()

            self.setup_logging()
            self.setup_config()
            self.setup_security()
            self.setup_theme()

            # Hide splash screen
            if splash:
                splash.hide()

            # Show legal disclaimer
            self.show_legal_disclaimer()

            # Show authentication
            if not self.show_authentication():
                return 0  # User cancelled authentication

            # Create and show main window
            self.create_main_window()

            # Start event loop
            return self.app.exec_()
            
        except Exception as e:
            logging.error(f"Application error: {e}")
            if self.app:
                QMessageBox.critical(
                    None, 
                    "Application Error", 
                    f"An error occurred while starting the application:\n{e}"
                )
            return 1
        
        finally:
            logging.info("TikTok Automation Tool closed")

def main():
    """Main entry point."""
    app = TikTokAutomationApp()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
