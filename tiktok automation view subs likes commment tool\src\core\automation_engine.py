"""
TikTok Automation Engine - COMPLETE FIXED VERSION
Core automation logic for TikTok interactions.

⚠️ EDUCATIONAL PURPOSE ONLY
This module is designed for educational purposes and research.
Users must comply with TikTok's Terms of Service and applicable laws.
"""

import time
import random
import logging
from typing import List, Dict, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from threading import Thread, Event
from queue import Queue

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

from utils.logger import AutomationLogger
from core.proxy_manager import ProxyManager
from core.captcha_solver import CaptchaSolver

class ActionType(Enum):
    """Types of automation actions."""
    LIKE = "like"
    VIEW = "view"
    FOLLOW = "follow"
    COMMENT = "comment"

@dataclass
class AutomationTask:
    """Represents an automation task."""
    action_type: ActionType
    target_url: str
    quantity: int
    custom_data: Optional[Dict] = None

@dataclass
class AutomationResult:
    """Result of an automation action."""
    action_type: ActionType
    target_url: str
    success: bool
    message: str
    timestamp: float

class AutomationEngine:
    """Core automation engine for TikTok interactions."""
    
    def __init__(self, config: Dict, logger: AutomationLogger):
        """Initialize automation engine.
        
        Args:
            config: Configuration dictionary
            logger: Automation logger instance
        """
        self.config = config
        self.logger = logger
        
        # Automation state
        self.is_running = False
        self.is_paused = False
        self.stop_event = Event()
        
        # Browser and drivers
        self.driver = None
        self.proxy_manager = ProxyManager(config.get('proxy', {}))
        self.captcha_solver = CaptchaSolver(config.get('captcha', {}))
        
        # Task management
        self.task_queue = Queue()
        self.results = []
        
        # Callbacks for UI updates
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None
        
        # Statistics
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'successful_actions': 0,
            'failed_actions': 0,
            'start_time': None,
            'end_time': None
        }
        
        # Rate limiting to prevent TikTok blocks
        self.last_action_time = 0
        self.min_delay_between_actions = 3  # Minimum 3 seconds between actions
        self.actions_per_minute = 0
        self.action_timestamps = []
        self.max_actions_per_minute = 15  # Conservative limit
    
    def set_callbacks(self, progress_callback: Callable = None, status_callback: Callable = None):
        """Set callback functions for UI updates.
        
        Args:
            progress_callback: Function to call for progress updates
            status_callback: Function to call for status updates
        """
        self.progress_callback = progress_callback
        self.status_callback = status_callback
    
    def setup_browser(self) -> bool:
        """Setup and configure the browser.
        
        Returns:
            True if browser setup successful, False otherwise
        """
        try:
            # TikTok-optimized Chrome options
            chrome_options = Options()
            
            # CRITICAL: Anti-detection options for TikTok
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            
            # TikTok-specific options
            chrome_options.add_argument("--disable-infobars")
            chrome_options.add_argument("--disable-notifications")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-translate")
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-field-trial-config")
            chrome_options.add_argument("--disable-back-forward-cache")
            chrome_options.add_argument("--disable-ipc-flooding-protection")
            
            # Performance options (keep JavaScript and plugins enabled for TikTok!)
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--no-default-browser-check")
            chrome_options.add_argument("--disable-default-apps")
            chrome_options.add_argument("--disable-extensions")
            # NOTE: NOT disabling plugins - TikTok needs them for video
            chrome_options.add_argument("--disable-sync")
            
            # Real browser user agent (latest Chrome)
            chrome_options.add_argument(
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/********* Safari/537.36"
            )
            
            # TikTok-friendly preferences (FIXED: prevent data: pages)
            chrome_options.add_experimental_option('prefs', {
                'intl.accept_languages': 'en-US,en',
                'profile.default_content_setting_values': {
                    'notifications': 2,
                    'media_stream': 1,
                    'media_stream_mic': 1,
                    'media_stream_camera': 1,
                    'geolocation': 1
                },
                'profile.default_content_settings.popups': 0,
                'profile.managed_default_content_settings': {
                    'images': 1  # Allow images for TikTok
                },
                'profile.content_settings.exceptions.automatic_downloads': {
                    '*': {'setting': 1}
                },
                # CRITICAL: Prevent data: page opening
                'browser.startup.homepage': 'https://www.tiktok.com',
                'browser.startup.page': 1,
                'session.restore_on_startup': 1,
                'profile.default_content_setting_values.data_url_in_svg_use_enabled': 2
            })
            
            # Window settings for TikTok
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--start-maximized")

            # CRITICAL: Prevent data: page opening
            chrome_options.add_argument("--disable-background-mode")
            chrome_options.add_argument("--disable-background-networking")
            chrome_options.add_argument("--disable-client-side-phishing-detection")
            chrome_options.add_argument("--disable-component-update")
            chrome_options.add_argument("--disable-default-apps")
            chrome_options.add_argument("--disable-hang-monitor")
            chrome_options.add_argument("--disable-prompt-on-repost")
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-features=TranslateUI")
            chrome_options.add_argument("--disable-ipc-flooding-protection")

            # Force normal startup (no data: page)
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--no-service-autorun")
            chrome_options.add_argument("--password-store=basic")
            chrome_options.add_argument("--use-mock-keychain")
            
            # Proxy configuration
            if self.config.get('use_proxy', False) and self.proxy_manager:
                proxy_config = self.proxy_manager.get_proxy_config()
                if proxy_config:
                    chrome_options.add_argument(f"--proxy-server={proxy_config}")
                    self.logger.log_action("browser_setup", "proxy", "configured", f"Using proxy: {proxy_config}")
            
            # Initialize driver
            self.driver = webdriver.Chrome(
                service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
                options=chrome_options
            )
            
            # Advanced TikTok stealth configuration
            stealth_script = """
                // Remove webdriver property
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                
                // Mock plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer'},
                        {name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai'},
                        {name: 'Native Client', filename: 'internal-nacl-plugin'}
                    ]
                });
                
                // Mock languages
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
                Object.defineProperty(navigator, 'language', {get: () => 'en-US'});
                
                // Mock chrome object
                window.chrome = {
                    runtime: {
                        onConnect: undefined,
                        onMessage: undefined
                    }
                };
                
                // Mock permissions
                Object.defineProperty(navigator, 'permissions', {
                    get: () => ({
                        query: () => Promise.resolve({state: 'granted'})
                    })
                });
                
                // Mock hardware concurrency
                Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 4});
                
                // Mock device memory
                Object.defineProperty(navigator, 'deviceMemory', {get: () => 8});
                
                // Override toString methods
                window.navigator.webdriver = undefined;
                
                // Remove automation indicators
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            """
            self.driver.execute_script(stealth_script)
            
            # TikTok-optimized timeouts
            self.driver.set_page_load_timeout(60)  # Extra long for TikTok
            self.driver.implicitly_wait(20)  # Longer implicit wait for dynamic content

            # CRITICAL FIX: Navigate to TikTok immediately to prevent data: page
            try:
                self.logger.log_action("browser_setup", "navigation", "starting", "Navigating to TikTok homepage")
                self.driver.get("https://www.tiktok.com")

                # Wait for TikTok to load
                WebDriverWait(self.driver, 30).until(
                    lambda driver: "tiktok.com" in driver.current_url.lower()
                )

                self.logger.log_action("browser_setup", "navigation", "success", f"Successfully loaded: {self.driver.current_url}")

            except Exception as e:
                self.logger.log_warning(f"Could not load TikTok homepage: {e}")
                # Continue anyway, navigation will happen in actions

            self.logger.log_action("browser_setup", "chrome", "success", "Browser initialized successfully")
            return True
            
        except Exception as e:
            self.logger.log_error(f"Browser setup failed: {e}")
            return False

    def add_task(self, task: AutomationTask):
        """Add task to the automation queue.

        Args:
            task: AutomationTask to add
        """
        self.task_queue.put(task)
        self.stats['total_tasks'] += 1

        self.logger.log_action(
            "task_added",
            task.target_url,
            "queued",
            f"Action: {task.action_type.value}, Quantity: {task.quantity}"
        )

    def start_automation(self) -> bool:
        """Start the automation process.

        Returns:
            True if started successfully, False otherwise
        """
        if self.is_running:
            self.logger.log_warning("Automation already running")
            return False

        # Setup browser
        if not self.setup_browser():
            return False

        # Reset state
        self.is_running = True
        self.is_paused = False
        self.stop_event.clear()
        self.stats['start_time'] = time.time()

        # Start automation thread
        automation_thread = Thread(target=self._automation_worker, daemon=True)
        automation_thread.start()

        self.logger.log_session_start("automation", self.stats['total_tasks'])

        if self.status_callback:
            self.status_callback("🚀 Automation started")

        return True

    def stop_automation(self):
        """Stop the automation process."""
        if not self.is_running:
            return

        self.stop_event.set()
        self.is_running = False
        self.stats['end_time'] = time.time()

        # Close browser
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                self.logger.log_error(f"Error closing browser: {e}")
            finally:
                self.driver = None

        # Log session end
        duration = self.stats['end_time'] - self.stats['start_time'] if self.stats['start_time'] else 0
        self.logger.log_session_end(
            "automation",
            self.stats['successful_actions'],
            self.stats['failed_actions'],
            duration
        )

        if self.status_callback:
            self.status_callback("⏹️ Automation stopped")

    def pause_automation(self):
        """Pause the automation process."""
        self.is_paused = True
        self.logger.log_action("automation", "system", "paused", "Automation paused by user")

        if self.status_callback:
            self.status_callback("⏸️ Automation paused")

    def resume_automation(self):
        """Resume the automation process."""
        self.is_paused = False
        self.logger.log_action("automation", "system", "resumed", "Automation resumed by user")

        if self.status_callback:
            self.status_callback("▶️ Automation resumed")

    def _validate_tiktok_url(self, url: str) -> bool:
        """Validate if URL is a valid TikTok URL.

        Args:
            url: URL to validate

        Returns:
            True if valid TikTok URL, False otherwise
        """
        try:
            # Basic TikTok URL patterns
            tiktok_patterns = [
                'tiktok.com/@',
                'tiktok.com/t/',
                'vm.tiktok.com/',
                'www.tiktok.com/@',
                'www.tiktok.com/t/',
                'm.tiktok.com/@',
                'https://tiktok.com/',
                'https://www.tiktok.com/',
                'https://m.tiktok.com/',
                'https://vm.tiktok.com/'
            ]

            url_lower = url.lower()

            # Check if URL contains TikTok domain
            for pattern in tiktok_patterns:
                if pattern in url_lower:
                    return True

            # Check for @username format (convert to full URL)
            if url.startswith('@') and len(url) > 1:
                return True

            return False

        except Exception as e:
            self.logger.log_error(f"URL validation error: {e}")
            return False

    def _normalize_tiktok_url(self, url: str) -> str:
        """Normalize TikTok URL to standard format.

        Args:
            url: URL to normalize

        Returns:
            Normalized TikTok URL
        """
        try:
            # Handle @username format
            if url.startswith('@'):
                return f"https://www.tiktok.com/{url}"

            # Handle username without @
            if not url.startswith('http') and '/' not in url:
                return f"https://www.tiktok.com/@{url}"

            # Ensure https
            if url.startswith('http://'):
                url = url.replace('http://', 'https://')
            elif not url.startswith('https://'):
                url = f"https://{url}"

            # Normalize domain
            url = url.replace('m.tiktok.com', 'www.tiktok.com')
            url = url.replace('vm.tiktok.com', 'www.tiktok.com')

            return url

        except Exception as e:
            self.logger.log_error(f"URL normalization error: {e}")
            return url

    def _automation_worker(self):
        """Main automation worker thread."""
        try:
            while self.is_running and not self.stop_event.is_set():
                # Check if paused
                if self.is_paused:
                    time.sleep(1)
                    continue

                # Check for tasks
                if self.task_queue.empty():
                    time.sleep(1)
                    continue

                # Get next task
                try:
                    task = self.task_queue.get_nowait()
                    self._process_task(task)
                    self.task_queue.task_done()

                except Exception as e:
                    self.logger.log_error(f"Error processing task: {e}")

                # Random delay between tasks
                delay = random.uniform(
                    self.config.get('min_delay', 1),
                    self.config.get('max_delay', 5)
                )
                time.sleep(delay)

        except Exception as e:
            self.logger.log_error(f"Automation worker error: {e}")

        finally:
            self.is_running = False

    def _process_task(self, task: AutomationTask):
        """Process a single automation task.

        Args:
            task: AutomationTask to process
        """
        self.logger.log_action(
            "task_start",
            task.target_url,
            "processing",
            f"Action: {task.action_type.value}, Quantity: {task.quantity}"
        )

        try:
            # Validate and normalize URL
            if not self._validate_tiktok_url(task.target_url):
                self._record_failure(task, f"Invalid TikTok URL: {task.target_url}")
                return

            # Normalize URL
            task.target_url = self._normalize_tiktok_url(task.target_url)
            self.logger.log_action("url_validation", task.target_url, "success", "URL validated and normalized")

            # Apply rate limiting
            self._apply_rate_limiting()

            # Process based on action type (each action handles its own navigation)
            if task.action_type == ActionType.LIKE:
                success = self._perform_like_action(task)
            elif task.action_type == ActionType.VIEW:
                success = self._perform_view_action(task)
            elif task.action_type == ActionType.FOLLOW:
                success = self._perform_follow_action(task)
            elif task.action_type == ActionType.COMMENT:
                success = self._perform_comment_action(task)
            else:
                self.logger.log_error(f"Unknown action type: {task.action_type}")
                success = False

            if success:
                self._record_success(task)
            else:
                self._record_failure(task, "Action execution failed")

        except Exception as e:
            self._record_failure(task, f"Task processing error: {e}")

        finally:
            self.stats['completed_tasks'] += 1
            self._update_progress()

    def _perform_like_action(self, task: AutomationTask) -> bool:
        """Perform REAL like action on TikTok video with retry logic."""
        try:
            self.logger.log_action("like", task.target_url, "starting", f"Starting {task.quantity} likes")

            success_count = 0
            max_retries = 3  # Maximum retries per like

            for i in range(task.quantity):
                if self.stop_event.is_set():
                    break

                like_success = False

                # Retry logic for each like
                for retry in range(max_retries):
                    try:
                        # Navigate to video for each like (fresh session)
                        self.driver.get(task.target_url)
                        time.sleep(random.uniform(3, 6))

                        # Wait for page to fully load with longer timeout on retries
                        timeout = 15 + (retry * 5)  # Increase timeout on retries
                        WebDriverWait(self.driver, timeout).until(
                            EC.presence_of_element_located((By.TAG_NAME, "video"))
                        )

                        # UPDATED TikTok like button selectors (2024)
                        like_selectors = [
                            # Primary TikTok selectors (most current)
                            'button[data-e2e="like-button"]',
                            'button[data-e2e="browse-like-icon"]',
                            'span[data-e2e="like-icon"]',
                            'div[data-e2e="like-icon"]',

                            # New TikTok selectors
                            'button[data-e2e="video-like-button"]',
                            'button[data-e2e="video-like"]',
                            'span[data-e2e="video-like-icon"]',

                            # Aria-label based (most reliable)
                            'button[aria-label="Like"]',
                            'button[aria-label*="like"]',
                            'button[aria-label*="Like"]',
                            'button[title="Like"]',
                            'button[title*="like"]',

                            # SVG-based selectors (heart icons)
                            'svg[data-e2e="like-icon"]',
                            'svg[fill="currentColor"][width="24"]',
                            'svg[fill="currentColor"][height="24"]',

                            # Class-based selectors
                            '.like-button',
                            '.video-like-button',
                            '.video-card-like',
                            '.like-wrapper button',
                            '.like-container button',

                            # Generic button selectors
                            'button[title*="♥"]',
                            'button[title*="❤"]',
                            'button[aria-label*="♥"]',
                            'button[aria-label*="❤"]',

                            # XPath selectors (fallback)
                            '//button[contains(@aria-label, "Like")]',
                            '//button[contains(@aria-label, "like")]',
                            '//button[contains(@data-e2e, "like")]',
                            '//span[contains(@data-e2e, "like")]',
                            '//div[contains(@data-e2e, "like")]',

                            # Mobile-specific selectors
                            '.mobile-like-button',
                            '[data-testid="like-button"]',
                            '[data-testid="video-like-button"]'
                        ]

                        like_clicked = False

                        # Try each selector
                        for selector in like_selectors:
                            try:
                                if selector.startswith('//'):
                                    # XPath selector
                                    elements = self.driver.find_elements(By.XPATH, selector)
                                else:
                                    # CSS selector
                                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                                for element in elements:
                                    if element.is_displayed() and element.is_enabled():
                                        # Scroll to element
                                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                                        time.sleep(1)

                                        # Try normal click first
                                        try:
                                            element.click()
                                            self.logger.log_action("like", task.target_url, "success", f"Like {i+1} clicked with {selector}")
                                            like_clicked = True
                                            break
                                        except:
                                            # Try JavaScript click
                                            try:
                                                self.driver.execute_script("arguments[0].click();", element)
                                                self.logger.log_action("like", task.target_url, "success", f"Like {i+1} JS clicked with {selector}")
                                                like_clicked = True
                                                break
                                            except:
                                                continue

                                if like_clicked:
                                    break

                            except Exception as e:
                                continue

                        if like_clicked:
                            like_success = True
                            self.logger.log_action("like", task.target_url, "success", f"Like {i+1} succeeded on attempt {retry+1}")
                            break
                        else:
                            if retry < max_retries - 1:
                                self.logger.log_error(f"Like {i+1} attempt {retry+1} failed, retrying...")
                                time.sleep(random.uniform(2, 4))  # Wait before retry
                            else:
                                self.logger.log_error(f"Like {i+1}: All {max_retries} attempts failed")

                    except Exception as e:
                        if retry < max_retries - 1:
                            self.logger.log_error(f"Like {i+1} attempt {retry+1} error: {e}, retrying...")
                            time.sleep(random.uniform(2, 4))
                        else:
                            self.logger.log_error(f"Like {i+1} failed after {max_retries} attempts: {e}")
                        continue

                if like_success:
                    success_count += 1
                    time.sleep(random.uniform(2, 5))

                # Update progress
                if self.progress_callback:
                    progress = ((i + 1) / task.quantity) * 100
                    self.progress_callback("likes", progress)

            if success_count > 0:
                self.logger.log_action("like", task.target_url, "completed", f"Successfully liked {success_count}/{task.quantity} times")
                return True
            else:
                self.logger.log_error("No likes were successful")
                return False

        except Exception as e:
            self.logger.log_error(f"Like action completely failed: {e}")
            return False

    def _perform_view_action(self, task: AutomationTask) -> bool:
        """Perform REAL view action on TikTok video."""
        try:
            self.logger.log_action("view", task.target_url, "starting", f"Starting {task.quantity} views")

            success_count = 0

            for i in range(task.quantity):
                if self.stop_event.is_set():
                    break

                try:
                    # Navigate to video (each view = fresh page load)
                    self.driver.get(task.target_url)
                    time.sleep(random.uniform(2, 4))

                    # Wait for video element to load
                    try:
                        video_element = WebDriverWait(self.driver, 15).until(
                            EC.presence_of_element_located((By.TAG_NAME, "video"))
                        )

                        # Scroll to video
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", video_element)
                        time.sleep(1)

                        # Try to play video if paused
                        try:
                            if video_element.get_property("paused"):
                                video_element.click()  # Click to play
                                time.sleep(1)
                        except:
                            pass

                        # Simulate watching the video
                        watch_duration = random.uniform(5, 15)  # Watch for 5-15 seconds
                        self.logger.log_action("view", task.target_url, "watching", f"View {i+1}: Watching for {watch_duration:.1f}s")

                        # Break watching into smaller chunks to simulate real behavior
                        chunks = int(watch_duration / 2)
                        for chunk in range(chunks):
                            if self.stop_event.is_set():
                                break
                            time.sleep(2)

                            # Random interactions during watching
                            if random.random() < 0.3:  # 30% chance
                                try:
                                    # Random scroll or mouse movement
                                    self.driver.execute_script("window.scrollBy(0, Math.random() * 100 - 50);")
                                except:
                                    pass

                        success_count += 1
                        self.logger.log_action("view", task.target_url, "success", f"View {i+1} completed")

                    except TimeoutException:
                        self.logger.log_error(f"View {i+1}: Video element not found")
                        # Still count as attempt, maybe video loaded differently
                        time.sleep(random.uniform(3, 6))
                        success_count += 1

                    # Random delay between views
                    if i < task.quantity - 1:
                        delay = random.uniform(3, 8)
                        time.sleep(delay)

                    # Update progress
                    if self.progress_callback:
                        progress = ((i + 1) / task.quantity) * 100
                        self.progress_callback("views", progress)

                except Exception as e:
                    self.logger.log_error(f"View {i+1} failed: {e}")
                    continue

            if success_count > 0:
                self.logger.log_action("view", task.target_url, "completed", f"Successfully generated {success_count}/{task.quantity} views")
                return True
            else:
                self.logger.log_error("No views were successful")
                return False

        except Exception as e:
            self.logger.log_error(f"View action completely failed: {e}")
            return False

    def _perform_follow_action(self, task: AutomationTask) -> bool:
        """Perform REAL follow action on TikTok user."""
        try:
            self.logger.log_action("follow", task.target_url, "starting", f"Starting {task.quantity} follows")

            success_count = 0

            for i in range(task.quantity):
                if self.stop_event.is_set():
                    break

                try:
                    # Navigate to user profile
                    self.driver.get(task.target_url)
                    time.sleep(random.uniform(3, 6))

                    # Wait for page to load
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )

                    # Comprehensive follow button selectors
                    follow_selectors = [
                        # Primary TikTok selectors
                        'button[data-e2e="follow-button"]',
                        'button[data-e2e="browse-follow"]',
                        'span[data-e2e="follow-button"]',

                        # Alternative selectors
                        'button[aria-label*="Follow"]',
                        'button[title*="Follow"]',

                        # Class-based selectors
                        '.follow-button',
                        '.user-follow-button',
                        '.profile-follow-button',

                        # Text-based selectors
                        '//button[contains(text(), "Follow")]',
                        '//button[contains(text(), "follow")]',
                        '//span[contains(text(), "Follow")]',

                        # Generic button selectors
                        'button[type="button"]'
                    ]

                    follow_clicked = False

                    # Try each selector
                    for selector in follow_selectors:
                        try:
                            if selector.startswith('//'):
                                # XPath selector
                                elements = self.driver.find_elements(By.XPATH, selector)
                            else:
                                # CSS selector
                                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                            for element in elements:
                                if element.is_displayed() and element.is_enabled():
                                    # Check button text to confirm it's a follow button
                                    button_text = element.text.lower()

                                    if "follow" in button_text and "following" not in button_text and "unfollow" not in button_text:
                                        # Scroll to element
                                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                                        time.sleep(1)

                                        # Try normal click first
                                        try:
                                            element.click()
                                            self.logger.log_action("follow", task.target_url, "success", f"Follow {i+1} clicked with {selector}")
                                            follow_clicked = True
                                            break
                                        except:
                                            # Try JavaScript click
                                            try:
                                                self.driver.execute_script("arguments[0].click();", element)
                                                self.logger.log_action("follow", task.target_url, "success", f"Follow {i+1} JS clicked with {selector}")
                                                follow_clicked = True
                                                break
                                            except:
                                                continue
                                    elif "following" in button_text or "unfollow" in button_text:
                                        self.logger.log_action("follow", task.target_url, "skipped", f"Follow {i+1}: Already following")
                                        follow_clicked = True
                                        break

                            if follow_clicked:
                                break

                        except Exception as e:
                            continue

                    if follow_clicked:
                        success_count += 1
                        time.sleep(random.uniform(2, 5))
                    else:
                        self.logger.log_error(f"Follow {i+1}: Could not find follow button")

                    # Update progress
                    if self.progress_callback:
                        progress = ((i + 1) / task.quantity) * 100
                        self.progress_callback("follows", progress)

                except Exception as e:
                    self.logger.log_error(f"Follow {i+1} failed: {e}")
                    continue

            if success_count > 0:
                self.logger.log_action("follow", task.target_url, "completed", f"Successfully followed {success_count}/{task.quantity} times")
                return True
            else:
                self.logger.log_error("No follows were successful")
                return False

        except Exception as e:
            self.logger.log_error(f"Follow action completely failed: {e}")
            return False

    def _perform_comment_action(self, task: AutomationTask) -> bool:
        """Perform REAL comment action on TikTok video."""
        try:
            self.logger.log_action("comment", task.target_url, "starting", f"Starting {task.quantity} comments")

            # Predefined comments
            comments = [
                "Amazing! 🔥", "Love this! ❤️", "So cool! 😍", "Great content! 👏",
                "Awesome! 🎉", "Nice! 👌", "Perfect! ✨", "Incredible! 🤩",
                "Fantastic! 🌟", "Beautiful! 💖", "Epic! 🚀", "Brilliant! 💡",
                "Outstanding! 🏆", "Wonderful! 🌈", "Impressive! 💪", "Stunning! ✨",
                "Magnificent! 🎊", "Excellent! 👍", "Superb! 🎯", "Marvelous! 🎭"
            ]

            success_count = 0

            for i in range(task.quantity):
                if self.stop_event.is_set():
                    break

                try:
                    # Navigate to video
                    self.driver.get(task.target_url)
                    time.sleep(random.uniform(3, 6))

                    # Wait for page to load
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.TAG_NAME, "video"))
                    )

                    # Find comment button
                    comment_selectors = [
                        'button[data-e2e="comment-button"]',
                        'button[data-e2e="browse-comment"]',
                        'span[data-e2e="comment-icon"]',
                        'div[data-e2e="comment-icon"]',
                        'button[aria-label*="comment"]',
                        'button[aria-label*="Comment"]',
                        '.comment-button',
                        '//button[contains(@aria-label, "comment")]',
                        '//button[contains(@data-e2e, "comment")]'
                    ]

                    comment_button_found = False

                    # Try to find and click comment button
                    for selector in comment_selectors:
                        try:
                            if selector.startswith('//'):
                                elements = self.driver.find_elements(By.XPATH, selector)
                            else:
                                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                            for element in elements:
                                if element.is_displayed() and element.is_enabled():
                                    # Scroll to element
                                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                                    time.sleep(1)

                                    # Click comment button
                                    try:
                                        element.click()
                                        comment_button_found = True
                                        break
                                    except:
                                        try:
                                            self.driver.execute_script("arguments[0].click();", element)
                                            comment_button_found = True
                                            break
                                        except:
                                            continue

                            if comment_button_found:
                                break

                        except Exception as e:
                            continue

                    if comment_button_found:
                        time.sleep(random.uniform(2, 4))

                        # Find comment input field
                        comment_input_selectors = [
                            'textarea[data-e2e="comment-input"]',
                            'input[data-e2e="comment-input"]',
                            'textarea[placeholder*="comment"]',
                            'input[placeholder*="comment"]',
                            'textarea[placeholder*="Comment"]',
                            'input[placeholder*="Comment"]',
                            '.comment-input',
                            'textarea',
                            'input[type="text"]'
                        ]

                        comment_input_found = False
                        comment_text = random.choice(comments)  # Define outside scope

                        for selector in comment_input_selectors:
                            try:
                                input_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                                for input_element in input_elements:
                                    if input_element.is_displayed() and input_element.is_enabled():
                                        # Select random comment
                                        comment_text = random.choice(comments)

                                        # Clear and type comment
                                        input_element.clear()
                                        time.sleep(0.5)
                                        input_element.send_keys(comment_text)
                                        time.sleep(random.uniform(1, 2))

                                        # Find and click post button
                                        post_selectors = [
                                            'button[data-e2e="comment-post"]',
                                            'button[aria-label*="Post"]',
                                            'button[title*="Post"]',
                                            'button[aria-label*="Send"]',
                                            'button[title*="Send"]',
                                            '//button[contains(text(), "Post")]',
                                            '//button[contains(text(), "Send")]',
                                            '//button[contains(text(), "post")]',
                                            '//button[contains(text(), "send")]'
                                        ]

                                        for post_selector in post_selectors:
                                            try:
                                                if post_selector.startswith('//'):
                                                    post_button = self.driver.find_element(By.XPATH, post_selector)
                                                else:
                                                    post_button = self.driver.find_element(By.CSS_SELECTOR, post_selector)

                                                if post_button.is_displayed() and post_button.is_enabled():
                                                    post_button.click()
                                                    comment_input_found = True
                                                    break
                                            except:
                                                continue

                                        if comment_input_found:
                                            break

                                if comment_input_found:
                                    break

                            except Exception as e:
                                continue

                        if comment_input_found:
                            success_count += 1
                            self.logger.log_action("comment", task.target_url, "success", f"Comment {i+1}: '{comment_text}' posted")
                        else:
                            self.logger.log_error(f"Comment {i+1}: Could not find comment input field")
                    else:
                        self.logger.log_error(f"Comment {i+1}: Could not find comment button")

                    # Random delay between comments
                    if i < task.quantity - 1:
                        time.sleep(random.uniform(5, 10))

                    # Update progress
                    if self.progress_callback:
                        progress = ((i + 1) / task.quantity) * 100
                        self.progress_callback("comments", progress)

                except Exception as e:
                    self.logger.log_error(f"Comment {i+1} failed: {e}")
                    continue

            if success_count > 0:
                self.logger.log_action("comment", task.target_url, "completed", f"Successfully posted {success_count}/{task.quantity} comments")
                return True
            else:
                self.logger.log_error("No comments were successful")
                return False

        except Exception as e:
            self.logger.log_error(f"Comment action completely failed: {e}")
            return False

    def _record_success(self, task: AutomationTask):
        """Record successful task completion.

        Args:
            task: Completed AutomationTask
        """
        result = AutomationResult(
            action_type=task.action_type,
            target_url=task.target_url,
            success=True,
            message="Action completed successfully",
            timestamp=time.time()
        )

        self.results.append(result)
        self.stats['successful_actions'] += 1

        self.logger.log_action(
            task.action_type.value,
            task.target_url,
            "success",
            f"Quantity: {task.quantity}"
        )

    def _record_failure(self, task: AutomationTask, error_message: str):
        """Record failed task completion.

        Args:
            task: Failed AutomationTask
            error_message: Error description
        """
        result = AutomationResult(
            action_type=task.action_type,
            target_url=task.target_url,
            success=False,
            message=error_message,
            timestamp=time.time()
        )

        self.results.append(result)
        self.stats['failed_actions'] += 1

        self.logger.log_action(
            task.action_type.value,
            task.target_url,
            "failed",
            error_message
        )

    def _update_progress(self):
        """Update overall progress."""
        if self.stats['total_tasks'] > 0:
            progress = (self.stats['completed_tasks'] / self.stats['total_tasks']) * 100

            if self.progress_callback:
                self.progress_callback("overall", progress)

    def _apply_rate_limiting(self):
        """Apply rate limiting to prevent TikTok blocks."""
        try:
            current_time = time.time()

            # Remove timestamps older than 1 minute
            self.action_timestamps = [t for t in self.action_timestamps if current_time - t < 60]

            # Check if we're exceeding rate limit
            if len(self.action_timestamps) >= self.max_actions_per_minute:
                wait_time = 60 - (current_time - self.action_timestamps[0])
                if wait_time > 0:
                    self.logger.log_action("rate_limiting", "system", "waiting", f"Rate limit reached, waiting {wait_time:.1f}s")
                    time.sleep(wait_time)

            # Ensure minimum delay between actions
            time_since_last = current_time - self.last_action_time
            if time_since_last < self.min_delay_between_actions:
                wait_time = self.min_delay_between_actions - time_since_last
                self.logger.log_action("rate_limiting", "system", "delay", f"Applying {wait_time:.1f}s delay")
                time.sleep(wait_time)

            # Record this action
            self.action_timestamps.append(time.time())
            self.last_action_time = time.time()

        except Exception as e:
            self.logger.log_error(f"Rate limiting error: {e}")

    def _recover_browser(self) -> bool:
        """Recover browser if it crashes or becomes unresponsive.

        Returns:
            True if recovery successful, False otherwise
        """
        try:
            self.logger.log_action("browser_recovery", "system", "starting", "Attempting browser recovery")

            # Try to close existing browser
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None

            # Wait a moment
            time.sleep(2)

            # Restart browser
            if self.setup_browser():
                self.logger.log_action("browser_recovery", "system", "success", "Browser recovered successfully")
                return True
            else:
                self.logger.log_error("Browser recovery failed")
                return False

        except Exception as e:
            self.logger.log_error(f"Browser recovery error: {e}")
            return False

    def _check_browser_health(self) -> bool:
        """Check if browser is still responsive.

        Returns:
            True if browser is healthy, False otherwise
        """
        try:
            if not self.driver:
                return False

            # Try a simple operation
            self.driver.current_url
            return True

        except Exception:
            return False

    def get_statistics(self) -> Dict:
        """Get current automation statistics.

        Returns:
            Dictionary containing statistics
        """
        stats = self.stats.copy()

        if stats['start_time'] and not stats['end_time']:
            stats['current_duration'] = time.time() - stats['start_time']
        elif stats['start_time'] and stats['end_time']:
            stats['total_duration'] = stats['end_time'] - stats['start_time']

        return stats
