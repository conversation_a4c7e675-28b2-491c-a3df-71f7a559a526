"""
Security and Authentication Module for TikTok Automation Tool
Handles user authentication, data encryption, and security features.
"""

import os
import hashlib
import secrets
import base64
import json
import time
from typing import Optional, Dict, Any
from pathlib import Path
import logging

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import bcrypt

class SecurityManager:
    """Manages security features and data encryption."""
    
    def __init__(self, config_dir: Path):
        """Initialize security manager.
        
        Args:
            config_dir: Directory for security configuration
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.key_file = self.config_dir / ".security_key"
        self.auth_file = self.config_dir / ".auth_data"
        
        # Initialize encryption
        self.cipher_suite = None
        self._init_encryption()
        
        # Session management
        self.session_data = {}
        self.session_timeout = 3600  # 1 hour default
        
        logging.info("Security manager initialized")
    
    def _init_encryption(self):
        """Initialize encryption system."""
        try:
            if self.key_file.exists():
                # Load existing key
                with open(self.key_file, 'rb') as f:
                    key = f.read()
            else:
                # Generate new key
                key = Fernet.generate_key()
                with open(self.key_file, 'wb') as f:
                    f.write(key)
                
                # Set file permissions (Windows)
                try:
                    os.chmod(self.key_file, 0o600)
                except OSError:
                    pass  # Windows doesn't support chmod the same way
            
            self.cipher_suite = Fernet(key)
            logging.info("Encryption system initialized")
            
        except Exception as e:
            logging.error(f"Failed to initialize encryption: {e}")
            self.cipher_suite = None
    
    def encrypt_data(self, data: str) -> Optional[str]:
        """Encrypt sensitive data.
        
        Args:
            data: Data to encrypt
            
        Returns:
            Encrypted data as base64 string or None if failed
        """
        if not self.cipher_suite:
            return data  # Return unencrypted if encryption not available
        
        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode('utf-8'))
            return base64.b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            logging.error(f"Encryption failed: {e}")
            return None
    
    def decrypt_data(self, encrypted_data: str) -> Optional[str]:
        """Decrypt sensitive data.
        
        Args:
            encrypted_data: Base64 encoded encrypted data
            
        Returns:
            Decrypted data or None if failed
        """
        if not self.cipher_suite:
            return encrypted_data  # Return as-is if encryption not available
        
        try:
            decoded_data = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self.cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode('utf-8')
        except Exception as e:
            logging.error(f"Decryption failed: {e}")
            return None
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt.
        
        Args:
            password: Plain text password
            
        Returns:
            Hashed password
        """
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash.
        
        Args:
            password: Plain text password
            hashed: Hashed password
            
        Returns:
            True if password matches
        """
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception as e:
            logging.error(f"Password verification failed: {e}")
            return False
    
    def generate_session_token(self) -> str:
        """Generate secure session token.
        
        Returns:
            Random session token
        """
        return secrets.token_urlsafe(32)
    
    def create_session(self, user_id: str, data: Dict[str, Any] = None) -> str:
        """Create new user session.
        
        Args:
            user_id: User identifier
            data: Additional session data
            
        Returns:
            Session token
        """
        token = self.generate_session_token()
        
        session_info = {
            'user_id': user_id,
            'created_at': time.time(),
            'last_activity': time.time(),
            'data': data or {}
        }
        
        self.session_data[token] = session_info
        logging.info(f"Session created for user: {user_id}")
        
        return token
    
    def validate_session(self, token: str) -> bool:
        """Validate session token.
        
        Args:
            token: Session token
            
        Returns:
            True if session is valid
        """
        if token not in self.session_data:
            return False
        
        session = self.session_data[token]
        current_time = time.time()
        
        # Check if session expired
        if current_time - session['last_activity'] > self.session_timeout:
            self.destroy_session(token)
            return False
        
        # Update last activity
        session['last_activity'] = current_time
        return True
    
    def get_session_data(self, token: str) -> Optional[Dict[str, Any]]:
        """Get session data.
        
        Args:
            token: Session token
            
        Returns:
            Session data or None if invalid
        """
        if self.validate_session(token):
            return self.session_data[token]
        return None
    
    def destroy_session(self, token: str):
        """Destroy session.
        
        Args:
            token: Session token
        """
        if token in self.session_data:
            user_id = self.session_data[token].get('user_id', 'unknown')
            del self.session_data[token]
            logging.info(f"Session destroyed for user: {user_id}")
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions."""
        current_time = time.time()
        expired_tokens = []
        
        for token, session in self.session_data.items():
            if current_time - session['last_activity'] > self.session_timeout:
                expired_tokens.append(token)
        
        for token in expired_tokens:
            self.destroy_session(token)
        
        if expired_tokens:
            logging.info(f"Cleaned up {len(expired_tokens)} expired sessions")

class AuthenticationManager:
    """Manages user authentication."""
    
    def __init__(self, security_manager: SecurityManager):
        """Initialize authentication manager.
        
        Args:
            security_manager: Security manager instance
        """
        self.security = security_manager
        self.auth_file = security_manager.auth_file
        self.users = {}
        self.current_session = None
        
        # Load existing users
        self._load_users()
        
        logging.info("Authentication manager initialized")
    
    def _load_users(self):
        """Load user data from file."""
        if self.auth_file.exists():
            try:
                with open(self.auth_file, 'r', encoding='utf-8') as f:
                    encrypted_data = f.read()
                
                if encrypted_data:
                    decrypted_data = self.security.decrypt_data(encrypted_data)
                    if decrypted_data:
                        self.users = json.loads(decrypted_data)
                        logging.info(f"Loaded {len(self.users)} user accounts")
            
            except Exception as e:
                logging.error(f"Failed to load user data: {e}")
                self.users = {}
    
    def _save_users(self):
        """Save user data to file."""
        try:
            user_data = json.dumps(self.users, indent=2)
            encrypted_data = self.security.encrypt_data(user_data)
            
            if encrypted_data:
                with open(self.auth_file, 'w', encoding='utf-8') as f:
                    f.write(encrypted_data)
                
                logging.info("User data saved successfully")
            
        except Exception as e:
            logging.error(f"Failed to save user data: {e}")
    
    def register_user(self, username: str, password: str, email: str = None) -> bool:
        """Register new user.
        
        Args:
            username: Username
            password: Password
            email: Optional email
            
        Returns:
            True if registration successful
        """
        if username in self.users:
            logging.warning(f"User already exists: {username}")
            return False
        
        # Validate password strength
        if not self._validate_password_strength(password):
            logging.warning("Password does not meet strength requirements")
            return False
        
        # Create user account
        user_data = {
            'username': username,
            'password_hash': self.security.hash_password(password),
            'email': email,
            'created_at': time.time(),
            'last_login': None,
            'login_count': 0,
            'is_active': True
        }
        
        self.users[username] = user_data
        self._save_users()
        
        logging.info(f"User registered: {username}")
        return True
    
    def authenticate_user(self, username: str, password: str) -> Optional[str]:
        """Authenticate user and create session.
        
        Args:
            username: Username
            password: Password
            
        Returns:
            Session token if successful, None otherwise
        """
        if username not in self.users:
            logging.warning(f"Authentication failed - user not found: {username}")
            return None
        
        user = self.users[username]
        
        if not user.get('is_active', True):
            logging.warning(f"Authentication failed - user inactive: {username}")
            return None
        
        if not self.security.verify_password(password, user['password_hash']):
            logging.warning(f"Authentication failed - invalid password: {username}")
            return None
        
        # Update login info
        user['last_login'] = time.time()
        user['login_count'] = user.get('login_count', 0) + 1
        self._save_users()
        
        # Create session
        session_token = self.security.create_session(username, {
            'email': user.get('email'),
            'login_count': user['login_count']
        })
        
        self.current_session = session_token
        logging.info(f"User authenticated: {username}")
        
        return session_token
    
    def logout_user(self):
        """Logout current user."""
        if self.current_session:
            self.security.destroy_session(self.current_session)
            self.current_session = None
            logging.info("User logged out")
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated.
        
        Returns:
            True if user is authenticated
        """
        if not self.current_session:
            return False
        
        return self.security.validate_session(self.current_session)
    
    def get_current_user(self) -> Optional[str]:
        """Get current authenticated user.
        
        Returns:
            Username if authenticated, None otherwise
        """
        if not self.is_authenticated():
            return None
        
        session_data = self.security.get_session_data(self.current_session)
        return session_data.get('user_id') if session_data else None
    
    def change_password(self, username: str, old_password: str, new_password: str) -> bool:
        """Change user password.
        
        Args:
            username: Username
            old_password: Current password
            new_password: New password
            
        Returns:
            True if password changed successfully
        """
        if username not in self.users:
            return False
        
        user = self.users[username]
        
        # Verify old password
        if not self.security.verify_password(old_password, user['password_hash']):
            logging.warning(f"Password change failed - invalid old password: {username}")
            return False
        
        # Validate new password
        if not self._validate_password_strength(new_password):
            logging.warning("New password does not meet strength requirements")
            return False
        
        # Update password
        user['password_hash'] = self.security.hash_password(new_password)
        self._save_users()
        
        logging.info(f"Password changed for user: {username}")
        return True
    
    def _validate_password_strength(self, password: str) -> bool:
        """Validate password strength.
        
        Args:
            password: Password to validate
            
        Returns:
            True if password meets requirements
        """
        if len(password) < 8:
            return False
        
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        return has_upper and has_lower and has_digit and has_special
    
    def get_user_info(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user information.
        
        Args:
            username: Username
            
        Returns:
            User information (without password hash)
        """
        if username not in self.users:
            return None
        
        user = self.users[username].copy()
        user.pop('password_hash', None)  # Remove sensitive data
        return user
    
    def deactivate_user(self, username: str) -> bool:
        """Deactivate user account.
        
        Args:
            username: Username
            
        Returns:
            True if deactivated successfully
        """
        if username not in self.users:
            return False
        
        self.users[username]['is_active'] = False
        self._save_users()
        
        logging.info(f"User deactivated: {username}")
        return True
