@echo off
title TikTok Automation Tool - FINAL Register Fix
color 0A
echo.
echo ████████╗██╗██╗  ██╗████████╗ ██████╗ ██╗  ██╗
echo ╚══██╔══╝██║██║ ██╔╝╚══██╔══╝██╔═══██╗██║ ██╔╝
echo    ██║   ██║█████╔╝    ██║   ██║   ██║█████╔╝ 
echo    ██║   ██║██╔═██╗    ██║   ██║   ██║██╔═██╗ 
echo    ██║   ██║██║  ██╗   ██║   ╚██████╔╝██║  ██╗
echo    ╚═╝   ╚═╝╚═╝  ╚═╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝
echo.
echo         FINAL REGISTER COLUMNS FIX
echo ================================================
echo.
echo 🎯 DEFINITIEVE REGISTER FIXES:
echo    • Dialog: 750x800 (extra groot)
echo    • Labels: 150px breed, rechts uitgelijnd
echo    • Input velden: 450px breed, 40px hoog
echo    • Horizontale spacing: 40px tussen kolommen
echo    • Verticale spacing: 30px tussen rijen
echo    • Fixed column widths: Geen overlap meer!
echo    • Perfect alignment: Alles netjes uitgelijnd
echo.
echo ✅ Login tab: BLIJFT ONGEWIJZIGD (perfect zoals het is)
echo 🔧 Register tab: DEFINITIEF GEFIXED
echo.

echo ⚠️  SLUIT DE HUIDIGE TIKTOK TOOL EERST!
echo.
pause

echo [1/3] Cleaning previous build...
if exist build rmdir /s /q build >nul 2>&1
if exist dist rmdir /s /q dist >nul 2>&1
echo ✅ Cleanup complete

echo.
echo [2/3] Building FINAL version with perfect register layout...
echo This will take 5-10 minutes...
echo.

call build_exe.bat

echo.
echo [3/3] Checking result...
if exist "dist\TikTokAutomationTool.exe" (
    echo.
    echo ████████████████████████████████████████████████████
    echo          🎉 REGISTER TAB DEFINITIEF GEFIXED! 🎉
    echo ████████████████████████████████████████████████████
    echo.
    echo ✅ FINALE VERSIE: dist\TikTokAutomationTool.exe
    echo.
    echo 🎯 PERFECTE REGISTER LAYOUT:
    echo    ✅ Labels: 150px breed, rechts uitgelijnd
    echo    ✅ Input velden: 450px breed, 40px hoog  
    echo    ✅ Spacing: 40px horizontaal, 30px verticaal
    echo    ✅ Dialog: 750x800 (ruim genoeg)
    echo    ✅ Geen overlap: Alles perfect gescheiden
    echo    ✅ Professional layout: Netjes uitgelijnd
    echo.
    echo 🧪 FINALE TEST:
    echo    1. Sluit oude versie
    echo    2. Start nieuwe TikTokAutomationTool.exe
    echo    3. Test Login tab (moet perfect blijven)
    echo    4. Test Register tab (moet nu perfect zijn):
    echo       - Username veld: breed en duidelijk
    echo       - Email veld: breed en duidelijk
    echo       - Password veld: breed en duidelijk  
    echo       - Confirm veld: breed en duidelijk
    echo    5. Alle velden moeten nu perfect bruikbaar zijn!
    echo.
    echo 📁 Opening dist folder...
    explorer dist
) else (
    echo.
    echo ❌ BUILD FAILED
    echo Check the error messages above
    echo.
)

echo.
echo 🎉 DIT IS DE DEFINITIEVE VERSIE!
echo    • Login tab: Perfect (ongewijzigd)
echo    • Register tab: Nu ook perfect
echo    • Geen verdere aanpassingen nodig
echo.
echo Press any key to exit...
pause >nul
