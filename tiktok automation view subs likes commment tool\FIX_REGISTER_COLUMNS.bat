@echo off
title TikTok Automation Tool - Register Columns Fix
color 0C
echo.
echo ████████╗██╗██╗  ██╗████████╗ ██████╗ ██╗  ██╗
echo ╚══██╔══╝██║██║ ██╔╝╚══██╔══╝██╔═══██╗██║ ██╔╝
echo    ██║   ██║█████╔╝    ██║   ██║   ██║█████╔╝ 
echo    ██║   ██║██╔═██╗    ██║   ██║   ██║██╔═██╗ 
echo    ██║   ██║██║  ██╗   ██║   ╚██████╔╝██║  ██╗
echo    ╚═╝   ╚═╝╚═╝  ╚═╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝
echo.
echo         FIXING REGISTER COLUMNS ISSUE
echo ================================================
echo.
echo 🔧 REGISTER TAB FIXES APPLIED:
echo    • Dialog size: 700x750 (was 600x700)
echo    • Input fields: 400px wide (was 350px)
echo    • Input height: 35px (was 25px)
echo    • Labels: Fixed 140px width
echo    • Spacing: 25px between elements
echo    • Column stretch: Force wide layout
echo    • Minimum column width: 400px
echo.

echo ⚠️  IMPORTANT: Close the current TikTok tool if running!
echo.
pause

echo [1/3] Cleaning previous build...
if exist build rmdir /s /q build >nul 2>&1
if exist dist rmdir /s /q dist >nul 2>&1
echo ✅ Cleanup complete

echo.
echo [2/3] Building with register column fixes...
echo This will take 5-10 minutes...
echo.

call build_exe.bat

echo.
echo [3/3] Checking result...
if exist "dist\TikTokAutomationTool.exe" (
    echo.
    echo ████████████████████████████████████████████████████
    echo            🎉 REGISTER COLUMNS FIXED! 🎉
    echo ████████████████████████████████████████████████████
    echo.
    echo ✅ New executable: dist\TikTokAutomationTool.exe
    echo.
    echo 🔧 REGISTER TAB IMPROVEMENTS:
    echo    ✅ Much wider input fields (400px)
    echo    ✅ Taller input fields (35px height)
    echo    ✅ Fixed label widths (140px)
    echo    ✅ Better spacing (25px)
    echo    ✅ Larger dialog (700x750)
    echo    ✅ Forced column layout
    echo.
    echo 🧪 TEST INSTRUCTIONS:
    echo    1. Close any running TikTok tool
    echo    2. Run the NEW TikTokAutomationTool.exe
    echo    3. Click on "Register" tab
    echo    4. Check if all fields are now wide enough:
    echo       - Username field
    echo       - Email field  
    echo       - Password field
    echo       - Confirm Password field
    echo    5. Try typing in each field
    echo.
    echo 📁 Opening dist folder...
    explorer dist
) else (
    echo.
    echo ❌ BUILD FAILED
    echo Check the error messages above
    echo.
)

echo.
echo ⚠️  REMEMBER: 
echo    • Close the old version first
echo    • Test the Register tab specifically
echo    • All fields should now be wide and usable
echo.
echo Press any key to exit...
pause >nul
