#!/usr/bin/env python3
"""
Test Script for TikTok Automation Tool
Tests basic functionality and UI components.
"""

import sys
import os
import unittest
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtTest import QTest
    from PyQt5.QtCore import Qt
    
    from utils.config import Config
    from utils.theme import ThemeManager
    from utils.logger import setup_logging
    from utils.security import SecurityManager, AuthenticationManager
    from ui.main_window import MainWindow
    from ui.login_dialog import LoginDialog
    from ui.splash_screen import SplashScreen
    from core.automation_controller import AutomationController
    from core.automation_engine import AutomationEngine, AutomationTask, ActionType
    from core.proxy_manager import ProxyManager
    from core.captcha_solver import CaptchaSolver
    
    PYQT_AVAILABLE = True
except ImportError as e:
    print(f"PyQt5 not available: {e}")
    PYQT_AVAILABLE = False

class TestConfig(unittest.TestCase):
    """Test configuration management."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = Path("test_config")
        self.test_dir.mkdir(exist_ok=True)
        self.config = Config(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_config_creation(self):
        """Test config creation and defaults."""
        self.assertIsNotNone(self.config)
        self.assertEqual(self.config.get('app', 'version'), '1.0.0')
        self.assertEqual(self.config.get('ui', 'theme'), 'dark')
    
    def test_config_set_get(self):
        """Test setting and getting config values."""
        self.config.set('test', 'key', 'value')
        self.assertEqual(self.config.get('test', 'key'), 'value')
    
    def test_config_save_load(self):
        """Test saving and loading config."""
        self.config.set('test', 'persistent', 'data')
        self.config.save()
        
        # Create new config instance
        new_config = Config(self.test_dir)
        self.assertEqual(new_config.get('test', 'persistent'), 'data')

@unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
class TestThemeManager(unittest.TestCase):
    """Test theme management."""
    
    def setUp(self):
        """Set up test environment."""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
        self.theme_manager = ThemeManager(self.app)
    
    def test_theme_creation(self):
        """Test theme manager creation."""
        self.assertIsNotNone(self.theme_manager)
        self.assertEqual(self.theme_manager.get_current_theme(), 'dark')
    
    def test_theme_switching(self):
        """Test theme switching."""
        self.theme_manager.set_theme('light')
        self.assertEqual(self.theme_manager.get_current_theme(), 'light')
        
        self.theme_manager.set_theme('dark')
        self.assertEqual(self.theme_manager.get_current_theme(), 'dark')
    
    def test_theme_toggle(self):
        """Test theme toggling."""
        original_theme = self.theme_manager.get_current_theme()
        self.theme_manager.toggle_theme()
        new_theme = self.theme_manager.get_current_theme()
        self.assertNotEqual(original_theme, new_theme)
    
    def test_theme_colors(self):
        """Test theme color retrieval."""
        colors = self.theme_manager.get_theme_colors('dark')
        self.assertIn('primary', colors)
        self.assertIn('background', colors)
        self.assertEqual(colors['primary'], '#FF0050')

@unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
class TestMainWindow(unittest.TestCase):
    """Test main window functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
        
        # Create test config
        self.test_dir = Path("test_ui_config")
        self.test_dir.mkdir(exist_ok=True)
        self.config = Config(self.test_dir)
        self.theme_manager = ThemeManager(self.app)
        
        # Create main window
        self.main_window = MainWindow(self.config, self.theme_manager)
    
    def tearDown(self):
        """Clean up test environment."""
        self.main_window.close()
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_window_creation(self):
        """Test main window creation."""
        self.assertIsNotNone(self.main_window)
        self.assertEqual(self.main_window.windowTitle(), "TikTok Automation Tool v1.0.0")
    
    def test_ui_components(self):
        """Test UI component existence."""
        # Check if main components exist
        self.assertIsNotNone(self.main_window.tab_widget)
        self.assertIsNotNone(self.main_window.target_input)
        self.assertIsNotNone(self.main_window.start_button)
        self.assertIsNotNone(self.main_window.stop_button)
    
    def test_input_validation(self):
        """Test input validation."""
        # Test empty target
        self.main_window.target_input.setText("")
        result = self.main_window._validate_inputs()
        self.assertFalse(result)
        
        # Test valid target
        self.main_window.target_input.setText("@testuser")
        self.main_window.likes_checkbox.setChecked(True)
        result = self.main_window._validate_inputs()
        self.assertTrue(result)
    
    def test_theme_switching_ui(self):
        """Test theme switching in UI."""
        original_theme = self.theme_manager.get_current_theme()
        
        # Simulate theme button click
        QTest.mouseClick(self.main_window.theme_button, Qt.LeftButton)
        
        new_theme = self.theme_manager.get_current_theme()
        self.assertNotEqual(original_theme, new_theme)

class TestLogging(unittest.TestCase):
    """Test logging functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_log_dir = Path("test_logs")
        self.test_log_dir.mkdir(exist_ok=True)
    
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if self.test_log_dir.exists():
            shutil.rmtree(self.test_log_dir)
    
    def test_logging_setup(self):
        """Test logging setup."""
        setup_logging(self.test_log_dir)
        
        # Check if log files are created
        import logging
        logging.info("Test log message")
        
        # Check if log directory has files
        log_files = list(self.test_log_dir.glob("*.log"))
        self.assertGreater(len(log_files), 0)

def run_basic_tests():
    """Run basic tests without PyQt5."""
    print("🧪 Running basic tests...")
    
    # Test imports
    try:
        from utils.config import Config
        from utils.logger import setup_logging
        print("✅ Core imports successful")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test config
    try:
        test_dir = Path("test_basic_config")
        test_dir.mkdir(exist_ok=True)
        config = Config(test_dir)
        config.set('test', 'basic', 'value')
        assert config.get('test', 'basic') == 'value'
        
        import shutil
        shutil.rmtree(test_dir)
        print("✅ Config tests passed")
    except Exception as e:
        print(f"❌ Config test error: {e}")
        return False
    
    # Test logging
    try:
        test_log_dir = Path("test_basic_logs")
        test_log_dir.mkdir(exist_ok=True)
        setup_logging(test_log_dir)
        
        import logging
        logging.info("Test message")
        
        import shutil
        shutil.rmtree(test_log_dir)
        print("✅ Logging tests passed")
    except Exception as e:
        print(f"❌ Logging test error: {e}")
        return False
    
    return True

def main():
    """Main test function."""
    print("🚀 TikTok Automation Tool - Test Suite")
    print("=" * 50)
    
    if not PYQT_AVAILABLE:
        print("⚠️  PyQt5 not available, running basic tests only")
        success = run_basic_tests()
        if success:
            print("\n✅ Basic tests completed successfully!")
        else:
            print("\n❌ Basic tests failed!")
        return
    
    # Run full test suite
    print("🧪 Running full test suite...")
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestConfig))
    suite.addTests(loader.loadTestsFromTestCase(TestThemeManager))
    suite.addTests(loader.loadTestsFromTestCase(TestMainWindow))
    suite.addTests(loader.loadTestsFromTestCase(TestLogging))
    suite.addTests(loader.loadTestsFromTestCase(TestAutomationEngine))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print results
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ All tests passed successfully!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed")
        print(f"❌ {len(result.errors)} error(s) occurred")
    
    print(f"📊 Tests run: {result.testsRun}")

    # Run performance tests
    print("\n" + "=" * 50)
    performance_success = run_performance_tests()

    print("=" * 50)

    if result.wasSuccessful() and performance_success:
        print("🎉 All tests completed successfully!")
    else:
        print("⚠️  Some tests failed or had issues")

class TestAutomationEngine(unittest.TestCase):
    """Test automation engine functionality."""

    def setUp(self):
        """Set up test environment."""
        self.test_log_dir = Path("test_automation_logs")
        self.test_log_dir.mkdir(exist_ok=True)

        from utils.logger import AutomationLogger
        self.logger = AutomationLogger(self.test_log_dir)

        self.config = {
            'headless_mode': True,
            'use_proxy': False,
            'default_delay': 1,
            'max_retries': 2,
            'timeout': 10
        }

    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if self.test_log_dir.exists():
            shutil.rmtree(self.test_log_dir)

    def test_action_types(self):
        """Test action type enumeration."""
        self.assertEqual(ActionType.LIKE.value, "like")
        self.assertEqual(ActionType.VIEW.value, "view")
        self.assertEqual(ActionType.FOLLOW.value, "follow")
        self.assertEqual(ActionType.COMMENT.value, "comment")

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete application."""

    def test_full_application_flow(self):
        """Test complete application initialization flow."""
        try:
            # Test config creation
            test_dir = Path("test_integration")
            test_dir.mkdir(exist_ok=True)

            config = Config(test_dir)
            self.assertIsNotNone(config)

            # Test security setup
            security_manager = SecurityManager(test_dir)
            auth_manager = AuthenticationManager(security_manager)
            self.assertIsNotNone(auth_manager)

            # Cleanup
            import shutil
            shutil.rmtree(test_dir)

        except Exception as e:
            self.fail(f"Integration test failed: {e}")

def run_performance_tests():
    """Run performance tests."""
    print("🚀 Running performance tests...")

    try:
        import time
        import psutil

        # Test memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024

        # Simulate heavy operations
        test_dir = Path("test_performance")
        test_dir.mkdir(exist_ok=True)

        start_time = time.time()

        # Create multiple config instances
        configs = []
        for i in range(10):
            config = Config(test_dir / f"config_{i}")
            configs.append(config)

        end_time = time.time()
        final_memory = process.memory_info().rss / 1024 / 1024

        print(f"✅ Performance test completed:")
        print(f"   Time taken: {end_time - start_time:.2f} seconds")
        print(f"   Memory usage: {initial_memory:.1f}MB -> {final_memory:.1f}MB")
        print(f"   Memory increase: {final_memory - initial_memory:.1f}MB")

        # Cleanup
        import shutil
        shutil.rmtree(test_dir)

        return True

    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

if __name__ == "__main__":
    main()
