"""
Main Window for TikTok Automation Tool
Professional UI with TikTok branding and modern design.
"""

import sys
import logging
from pathlib import Path
from typing import Optional

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTabWidget, QGroupBox, QLabel, QPushButton, QLineEdit, QTextEdit,
    QSpinBox, QComboBox, QProgressBar, QCheckBox, QSplitter,
    QMenuBar, QStatusBar, QAction, QMessageBox, QFileDialog,
    QScrollArea, QFrame, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize
from PyQt5.QtGui import QIcon, QPixmap, QFont, QPalette

from utils.config import Config
from utils.theme import ThemeManager
from utils.logger import AutomationLogger, PerformanceLogger
from utils.security import AuthenticationManager
from core.automation_controller import AutomationController

class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self, config: Config, theme_manager: ThemeManager, auth_manager: AuthenticationManager = None, current_user: str = None):
        """Initialize main window.

        Args:
            config: Configuration manager
            theme_manager: Theme manager
            auth_manager: Authentication manager
            current_user: Current authenticated user
        """
        super().__init__()

        self.config = config
        self.theme_manager = theme_manager
        self.auth_manager = auth_manager
        self.current_user = current_user or "guest"
        self.automation_logger = None
        self.performance_logger = None

        # Automation controller
        self.automation_controller = AutomationController(self.config, self)
        self._connect_automation_signals()
        
        # Window properties
        self.setWindowTitle("TikTok Automation Tool v1.0.0")
        self.setMinimumSize(1000, 700)
        
        # Load window geometry
        self._load_window_geometry()
        
        # Initialize UI components
        self._init_ui()
        self._init_menu_bar()
        self._init_status_bar()
        self._connect_signals()
        
        # Setup loggers
        self._setup_loggers()
        
        logging.info("Main window initialized successfully")
    
    def _load_window_geometry(self):
        """Load saved window geometry."""
        width = self.config.get('ui', 'window_width', 1200)
        height = self.config.get('ui', 'window_height', 800)
        maximized = self.config.get('ui', 'window_maximized', False)
        
        self.resize(width, height)
        
        if maximized:
            self.showMaximized()
        else:
            # Center window on screen
            screen = self.screen().availableGeometry()
            x = (screen.width() - width) // 2
            y = (screen.height() - height) // 2
            self.move(x, y)
    
    def _init_ui(self):
        """Initialize user interface."""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Header section
        header_widget = self._create_header()
        main_layout.addWidget(header_widget)
        
        # Main content area with tabs
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # Create tabs
        self._create_automation_tab()
        self._create_settings_tab()
        self._create_logs_tab()
        self._create_about_tab()
        
        main_layout.addWidget(self.tab_widget)
        
        # Footer section
        footer_widget = self._create_footer()
        main_layout.addWidget(footer_widget)
    
    def _create_header(self) -> QWidget:
        """Create header section with logo and controls."""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Logo and title
        logo_layout = QHBoxLayout()
        
        # TikTok logo placeholder
        logo_label = QLabel("🎵")
        logo_label.setStyleSheet("font-size: 32px;")
        logo_layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel("TikTok Automation Tool")
        title_label.setProperty("class", "title")
        logo_layout.addWidget(title_label)
        
        logo_layout.addStretch()
        header_layout.addLayout(logo_layout)
        
        # Control buttons
        controls_layout = QHBoxLayout()
        
        # Theme toggle button
        self.theme_button = QPushButton("🌙")
        self.theme_button.setToolTip("Toggle Dark/Light Theme")
        self.theme_button.setFixedSize(40, 40)
        self.theme_button.clicked.connect(self._toggle_theme)
        controls_layout.addWidget(self.theme_button)
        
        # Settings button
        settings_button = QPushButton("⚙️")
        settings_button.setToolTip("Settings")
        settings_button.setFixedSize(40, 40)
        settings_button.clicked.connect(lambda: self.tab_widget.setCurrentIndex(1))
        controls_layout.addWidget(settings_button)
        
        header_layout.addLayout(controls_layout)
        
        return header_widget
    
    def _create_automation_tab(self):
        """Create automation tab."""
        automation_widget = QWidget()
        layout = QVBoxLayout(automation_widget)
        layout.setSpacing(20)
        
        # Create splitter for left and right panels
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Input controls
        left_panel = self._create_input_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Progress and logs
        right_panel = self._create_progress_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 600])
        
        layout.addWidget(splitter)
        
        self.tab_widget.addTab(automation_widget, "🚀 Automation")
    
    def _create_input_panel(self) -> QWidget:
        """Create input controls panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # Target input group
        target_group = QGroupBox("Target Configuration")
        target_layout = QGridLayout(target_group)
        
        # TikTok URL/Username input
        target_layout.addWidget(QLabel("TikTok URL or Username:"), 0, 0)
        self.target_input = QLineEdit()
        self.target_input.setPlaceholderText("https://www.tiktok.com/@username or @username")
        target_layout.addWidget(self.target_input, 0, 1)
        
        # Browse button for batch file
        browse_button = QPushButton("Browse File")
        browse_button.clicked.connect(self._browse_target_file)
        target_layout.addWidget(browse_button, 1, 1)
        
        layout.addWidget(target_group)
        
        # Action selection group
        action_group = QGroupBox("Actions to Perform")
        action_layout = QGridLayout(action_group)
        
        # Checkboxes for different actions
        self.likes_checkbox = QCheckBox("Generate Likes")
        self.views_checkbox = QCheckBox("Generate Views")
        self.follows_checkbox = QCheckBox("Generate Followers")
        self.comments_checkbox = QCheckBox("Generate Comments")
        
        action_layout.addWidget(self.likes_checkbox, 0, 0)
        action_layout.addWidget(self.views_checkbox, 0, 1)
        action_layout.addWidget(self.follows_checkbox, 1, 0)
        action_layout.addWidget(self.comments_checkbox, 1, 1)
        
        layout.addWidget(action_group)
        
        # Quantity settings group
        quantity_group = QGroupBox("Quantity Settings")
        quantity_layout = QGridLayout(quantity_group)
        
        # Spinboxes for quantities
        quantity_layout.addWidget(QLabel("Likes:"), 0, 0)
        self.likes_spinbox = QSpinBox()
        self.likes_spinbox.setRange(1, 10000)
        self.likes_spinbox.setValue(100)
        quantity_layout.addWidget(self.likes_spinbox, 0, 1)
        
        quantity_layout.addWidget(QLabel("Views:"), 1, 0)
        self.views_spinbox = QSpinBox()
        self.views_spinbox.setRange(1, 100000)
        self.views_spinbox.setValue(1000)
        quantity_layout.addWidget(self.views_spinbox, 1, 1)
        
        quantity_layout.addWidget(QLabel("Followers:"), 2, 0)
        self.follows_spinbox = QSpinBox()
        self.follows_spinbox.setRange(1, 5000)
        self.follows_spinbox.setValue(50)
        quantity_layout.addWidget(self.follows_spinbox, 2, 1)
        
        quantity_layout.addWidget(QLabel("Comments:"), 3, 0)
        self.comments_spinbox = QSpinBox()
        self.comments_spinbox.setRange(1, 1000)
        self.comments_spinbox.setValue(20)
        quantity_layout.addWidget(self.comments_spinbox, 3, 1)
        
        layout.addWidget(quantity_group)
        
        # Comments configuration
        comments_group = QGroupBox("Custom Comments")
        comments_layout = QVBoxLayout(comments_group)
        
        self.comments_text = QTextEdit()
        self.comments_text.setPlaceholderText(
            "Enter custom comments (one per line):\n"
            "Amazing content! 🔥\n"
            "Love this! ❤️\n"
            "So cool! 😍"
        )
        self.comments_text.setMaximumHeight(100)
        comments_layout.addWidget(self.comments_text)
        
        layout.addWidget(comments_group)
        
        # Control buttons
        buttons_layout = QHBoxLayout()
        
        self.start_button = QPushButton("🚀 Start Automation")
        self.start_button.setProperty("class", "primary")
        self.start_button.clicked.connect(self._start_automation)
        buttons_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("⏹️ Stop")
        self.stop_button.setProperty("class", "secondary")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self._stop_automation)
        buttons_layout.addWidget(self.stop_button)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return panel
    
    def _create_progress_panel(self) -> QWidget:
        """Create progress monitoring panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # Progress group
        progress_group = QGroupBox("Progress Monitor")
        progress_layout = QVBoxLayout(progress_group)
        
        # Overall progress
        progress_layout.addWidget(QLabel("Overall Progress:"))
        self.overall_progress = QProgressBar()
        self.overall_progress.setTextVisible(True)
        progress_layout.addWidget(self.overall_progress)
        
        # Individual progress bars
        self.likes_progress = QProgressBar()
        self.likes_progress.setTextVisible(True)
        progress_layout.addWidget(QLabel("Likes Progress:"))
        progress_layout.addWidget(self.likes_progress)
        
        self.views_progress = QProgressBar()
        self.views_progress.setTextVisible(True)
        progress_layout.addWidget(QLabel("Views Progress:"))
        progress_layout.addWidget(self.views_progress)
        
        layout.addWidget(progress_group)
        
        # Status group
        status_group = QGroupBox("Live Status")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(200)
        status_layout.addWidget(self.status_text)
        
        layout.addWidget(status_group)
        
        # Statistics group
        stats_group = QGroupBox("Session Statistics")
        stats_layout = QGridLayout(stats_group)
        
        # Statistics labels
        stats_layout.addWidget(QLabel("Successful Actions:"), 0, 0)
        self.success_label = QLabel("0")
        self.success_label.setStyleSheet("color: #00D084; font-weight: bold;")
        stats_layout.addWidget(self.success_label, 0, 1)
        
        stats_layout.addWidget(QLabel("Failed Actions:"), 1, 0)
        self.failed_label = QLabel("0")
        self.failed_label.setStyleSheet("color: #FF3040; font-weight: bold;")
        stats_layout.addWidget(self.failed_label, 1, 1)
        
        stats_layout.addWidget(QLabel("Session Duration:"), 2, 0)
        self.duration_label = QLabel("00:00:00")
        stats_layout.addWidget(self.duration_label, 2, 1)
        
        layout.addWidget(stats_group)
        layout.addStretch()
        
        return panel

    def _create_settings_tab(self):
        """Create settings tab."""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)
        layout.setSpacing(20)

        # Create scroll area for settings
        scroll_area = QScrollArea()
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # General settings group
        general_group = QGroupBox("General Settings")
        general_layout = QGridLayout(general_group)

        # Delay settings
        general_layout.addWidget(QLabel("Action Delay (seconds):"), 0, 0)
        self.delay_spinbox = QSpinBox()
        self.delay_spinbox.setRange(1, 60)
        self.delay_spinbox.setValue(self.config.get('automation', 'default_delay', 2))
        general_layout.addWidget(self.delay_spinbox, 0, 1)

        # Max retries
        general_layout.addWidget(QLabel("Max Retries:"), 1, 0)
        self.retries_spinbox = QSpinBox()
        self.retries_spinbox.setRange(1, 10)
        self.retries_spinbox.setValue(self.config.get('automation', 'max_retries', 3))
        general_layout.addWidget(self.retries_spinbox, 1, 1)

        # Headless mode
        self.headless_checkbox = QCheckBox("Headless Mode (Hide Browser)")
        self.headless_checkbox.setChecked(self.config.get('automation', 'headless_mode', False))
        general_layout.addWidget(self.headless_checkbox, 2, 0, 1, 2)

        scroll_layout.addWidget(general_group)

        # Proxy settings group
        proxy_group = QGroupBox("Proxy Settings")
        proxy_layout = QGridLayout(proxy_group)

        # Enable proxy
        self.proxy_checkbox = QCheckBox("Use Proxy")
        self.proxy_checkbox.setChecked(self.config.get('automation', 'use_proxy', False))
        proxy_layout.addWidget(self.proxy_checkbox, 0, 0, 1, 2)

        # Proxy type
        proxy_layout.addWidget(QLabel("Proxy Type:"), 1, 0)
        self.proxy_type_combo = QComboBox()
        self.proxy_type_combo.addItems(["HTTP", "HTTPS", "SOCKS4", "SOCKS5"])
        proxy_layout.addWidget(self.proxy_type_combo, 1, 1)

        # Proxy host
        proxy_layout.addWidget(QLabel("Proxy Host:"), 2, 0)
        self.proxy_host_input = QLineEdit()
        self.proxy_host_input.setPlaceholderText("127.0.0.1")
        proxy_layout.addWidget(self.proxy_host_input, 2, 1)

        # Proxy port
        proxy_layout.addWidget(QLabel("Proxy Port:"), 3, 0)
        self.proxy_port_spinbox = QSpinBox()
        self.proxy_port_spinbox.setRange(1, 65535)
        self.proxy_port_spinbox.setValue(8080)
        proxy_layout.addWidget(self.proxy_port_spinbox, 3, 1)

        scroll_layout.addWidget(proxy_group)

        # Captcha settings group
        captcha_group = QGroupBox("Captcha Settings")
        captcha_layout = QGridLayout(captcha_group)

        # Captcha service
        captcha_layout.addWidget(QLabel("Captcha Service:"), 0, 0)
        self.captcha_service_combo = QComboBox()
        self.captcha_service_combo.addItems(["None", "2captcha", "Anti-Captcha"])
        captcha_layout.addWidget(self.captcha_service_combo, 0, 1)

        # API key
        captcha_layout.addWidget(QLabel("API Key:"), 1, 0)
        self.captcha_api_input = QLineEdit()
        self.captcha_api_input.setPlaceholderText("Enter your captcha service API key")
        self.captcha_api_input.setEchoMode(QLineEdit.Password)
        captcha_layout.addWidget(self.captcha_api_input, 1, 1)

        scroll_layout.addWidget(captcha_group)

        # Save settings button
        save_button = QPushButton("💾 Save Settings")
        save_button.clicked.connect(self._save_settings)
        scroll_layout.addWidget(save_button)

        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_content)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        self.tab_widget.addTab(settings_widget, "⚙️ Settings")

    def _create_logs_tab(self):
        """Create logs tab."""
        logs_widget = QWidget()
        layout = QVBoxLayout(logs_widget)

        # Log controls
        controls_layout = QHBoxLayout()

        # Log level filter
        controls_layout.addWidget(QLabel("Log Level:"))
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["ALL", "DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        controls_layout.addWidget(self.log_level_combo)

        controls_layout.addStretch()

        # Clear logs button
        clear_button = QPushButton("🗑️ Clear Logs")
        clear_button.clicked.connect(self._clear_logs)
        controls_layout.addWidget(clear_button)

        # Export logs button
        export_button = QPushButton("📤 Export Logs")
        export_button.clicked.connect(self._export_logs)
        controls_layout.addWidget(export_button)

        layout.addLayout(controls_layout)

        # Log display
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_display)

        self.tab_widget.addTab(logs_widget, "📋 Logs")

    def _create_about_tab(self):
        """Create about tab."""
        about_widget = QWidget()
        layout = QVBoxLayout(about_widget)
        layout.setAlignment(Qt.AlignCenter)

        # Logo and title
        title_label = QLabel("TikTok Automation Tool")
        title_label.setProperty("class", "title")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Version
        version_label = QLabel("Version 1.0.0")
        version_label.setProperty("class", "subtitle")
        version_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(version_label)

        # Description
        description = QLabel(
            "Professional TikTok automation tool for educational purposes.\n\n"
            "Features:\n"
            "• Automated likes, views, followers, and comments\n"
            "• Proxy support for anonymity\n"
            "• Captcha bypass integration\n"
            "• Modern TikTok-inspired UI\n"
            "• Comprehensive logging and monitoring\n\n"
            "⚠️ LEGAL DISCLAIMER:\n"
            "This tool is for educational purposes only.\n"
            "Users must comply with TikTok's Terms of Service\n"
            "and all applicable laws and regulations."
        )
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        description.setProperty("class", "description")
        layout.addWidget(description)

        # Links
        links_layout = QHBoxLayout()

        github_button = QPushButton("📚 Documentation")
        github_button.clicked.connect(lambda: self._open_url("https://github.com"))
        links_layout.addWidget(github_button)

        support_button = QPushButton("💬 Support")
        support_button.clicked.connect(lambda: self._open_url("mailto:<EMAIL>"))
        links_layout.addWidget(support_button)

        layout.addLayout(links_layout)
        layout.addStretch()

        self.tab_widget.addTab(about_widget, "ℹ️ About")

    def _create_footer(self) -> QWidget:
        """Create footer section."""
        footer_widget = QWidget()
        footer_layout = QHBoxLayout(footer_widget)
        footer_layout.setContentsMargins(0, 10, 0, 0)

        # Status indicator
        self.status_indicator = QLabel("🔴 Stopped")
        footer_layout.addWidget(self.status_indicator)

        footer_layout.addStretch()

        # Connection status
        self.connection_status = QLabel("🌐 Offline")
        footer_layout.addWidget(self.connection_status)

        return footer_widget

    def _init_menu_bar(self):
        """Initialize menu bar."""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("File")

        # Import settings
        import_action = QAction("Import Settings", self)
        import_action.triggered.connect(self._import_settings)
        file_menu.addAction(import_action)

        # Export settings
        export_action = QAction("Export Settings", self)
        export_action.triggered.connect(self._export_settings)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # Exit
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu("Tools")

        # Clear all data
        clear_action = QAction("Clear All Data", self)
        clear_action.triggered.connect(self._clear_all_data)
        tools_menu.addAction(clear_action)

        # Help menu
        help_menu = menubar.addMenu("Help")

        # Documentation
        docs_action = QAction("Documentation", self)
        docs_action.triggered.connect(lambda: self._open_url("https://github.com"))
        help_menu.addAction(docs_action)

        # About
        about_action = QAction("About", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)

    def _init_status_bar(self):
        """Initialize status bar."""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # Add permanent widgets
        self.status_bar.showMessage("Ready")

        # Add permanent status indicators
        self.memory_label = QLabel("Memory: 0 MB")
        self.status_bar.addPermanentWidget(self.memory_label)

        self.cpu_label = QLabel("CPU: 0%")
        self.status_bar.addPermanentWidget(self.cpu_label)

    def _connect_signals(self):
        """Connect signals and slots."""
        # Theme manager signals
        self.theme_manager.theme_changed.connect(self._on_theme_changed)

        # Checkbox signals for enabling/disabling spinboxes
        self.likes_checkbox.toggled.connect(lambda checked: self.likes_spinbox.setEnabled(checked))
        self.views_checkbox.toggled.connect(lambda checked: self.views_spinbox.setEnabled(checked))
        self.follows_checkbox.toggled.connect(lambda checked: self.follows_spinbox.setEnabled(checked))
        self.comments_checkbox.toggled.connect(lambda checked: self.comments_spinbox.setEnabled(checked))

        # Proxy checkbox signal
        self.proxy_checkbox.toggled.connect(self._on_proxy_toggled)

    def _connect_automation_signals(self):
        """Connect automation controller signals."""
        self.automation_controller.progress_updated.connect(self._on_automation_progress)
        self.automation_controller.status_updated.connect(self._on_automation_status)
        self.automation_controller.statistics_updated.connect(self._on_automation_statistics)
        self.automation_controller.automation_finished.connect(self._on_automation_finished)

    def _setup_loggers(self):
        """Setup specialized loggers."""
        log_dir = Path(__file__).parent.parent.parent / "logs"
        self.automation_logger = AutomationLogger(log_dir)
        self.performance_logger = PerformanceLogger(log_dir)

    # Event handlers
    def _toggle_theme(self):
        """Toggle between dark and light themes."""
        self.theme_manager.toggle_theme()

        # Update theme button icon
        if self.theme_manager.get_current_theme() == "dark":
            self.theme_button.setText("🌙")
        else:
            self.theme_button.setText("☀️")

    def _on_theme_changed(self, theme_name: str):
        """Handle theme change."""
        # Save theme preference
        self.config.set('ui', 'theme', theme_name)
        self.config.save()

        logging.info(f"Theme changed to: {theme_name}")

    def _on_proxy_toggled(self, checked: bool):
        """Handle proxy checkbox toggle."""
        # Enable/disable proxy controls
        self.proxy_type_combo.setEnabled(checked)
        self.proxy_host_input.setEnabled(checked)
        self.proxy_port_spinbox.setEnabled(checked)

    def _browse_target_file(self):
        """Browse for target file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Target File",
            "",
            "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            self.target_input.setText(file_path)

    def _start_automation(self):
        """Start automation process."""
        # Validate inputs
        if not self._validate_inputs():
            return

        # Prepare automation data
        target_url = self.target_input.text().strip()

        # Collect selected actions
        actions = {}
        if self.likes_checkbox.isChecked():
            actions['like'] = self.likes_spinbox.value()
        if self.views_checkbox.isChecked():
            actions['view'] = self.views_spinbox.value()
        if self.follows_checkbox.isChecked():
            actions['follow'] = self.follows_spinbox.value()
        if self.comments_checkbox.isChecked():
            actions['comment'] = self.comments_spinbox.value()

        # Get custom comments
        custom_comments = []
        if self.comments_text.toPlainText().strip():
            custom_comments = [
                line.strip() for line in self.comments_text.toPlainText().split('\n')
                if line.strip()
            ]

        # Add automation task
        self.automation_controller.add_automation_task(target_url, actions, custom_comments)

        # Start automation
        if self.automation_controller.start_automation():
            # Update UI state
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_indicator.setText("🟢 Running")

            # Reset progress bars
            self.overall_progress.setValue(0)
            self.likes_progress.setValue(0)
            self.views_progress.setValue(0)

            # Reset statistics
            self.success_label.setText("0")
            self.failed_label.setText("0")
            self.duration_label.setText("00:00:00")

            # Add status message
            self._add_status_message("🚀 Automation started...")

            logging.info("Automation process started")
        else:
            QMessageBox.warning(self, "Automation Error", "Failed to start automation. Check logs for details.")

    def _stop_automation(self):
        """Stop automation process."""
        # Stop automation controller
        self.automation_controller.stop_automation()

        # Update UI state
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_indicator.setText("🔴 Stopped")

        # Add status message
        self._add_status_message("⏹️ Automation stopped by user")

        logging.info("Automation process stopped")

    def _validate_inputs(self) -> bool:
        """Validate user inputs."""
        # Check if target is provided
        if not self.target_input.text().strip():
            QMessageBox.warning(self, "Validation Error", "Please enter a TikTok URL or username.")
            return False

        # Check if at least one action is selected
        if not any([
            self.likes_checkbox.isChecked(),
            self.views_checkbox.isChecked(),
            self.follows_checkbox.isChecked(),
            self.comments_checkbox.isChecked()
        ]):
            QMessageBox.warning(self, "Validation Error", "Please select at least one action to perform.")
            return False

        return True

    def _add_status_message(self, message: str):
        """Add message to status display."""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        self.status_text.append(formatted_message)

        # Auto-scroll to bottom
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.End)
        self.status_text.setTextCursor(cursor)

    def _save_settings(self):
        """Save current settings."""
        # Save automation settings
        self.config.set('automation', 'default_delay', self.delay_spinbox.value())
        self.config.set('automation', 'max_retries', self.retries_spinbox.value())
        self.config.set('automation', 'headless_mode', self.headless_checkbox.isChecked())
        self.config.set('automation', 'use_proxy', self.proxy_checkbox.isChecked())

        # Save proxy settings
        self.config.set_automation_setting('proxy_type', self.proxy_type_combo.currentText())
        self.config.set_automation_setting('proxy_host', self.proxy_host_input.text())
        self.config.set_automation_setting('proxy_port', self.proxy_port_spinbox.value())

        # Save captcha settings
        self.config.set_automation_setting('captcha_service', self.captcha_service_combo.currentText())
        self.config.set_automation_setting('captcha_api_key', self.captcha_api_input.text())

        # Save all configurations
        self.config.save()

        QMessageBox.information(self, "Settings", "Settings saved successfully!")
        logging.info("Settings saved")

    def _clear_logs(self):
        """Clear log display."""
        self.log_display.clear()
        self._add_status_message("📋 Logs cleared")

    def _export_logs(self):
        """Export logs to file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Logs",
            f"tiktok_automation_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_display.toPlainText())

                QMessageBox.information(self, "Export", f"Logs exported to:\n{file_path}")
                logging.info(f"Logs exported to: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export logs:\n{e}")
                logging.error(f"Failed to export logs: {e}")

    def _import_settings(self):
        """Import settings from file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Import Settings",
            "",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            try:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # Apply imported settings
                # TODO: Implement settings import logic

                QMessageBox.information(self, "Import", "Settings imported successfully!")
                logging.info(f"Settings imported from: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Import Error", f"Failed to import settings:\n{e}")
                logging.error(f"Failed to import settings: {e}")

    def _export_settings(self):
        """Export settings to file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Settings",
            f"tiktok_automation_settings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            try:
                import json
                settings = {
                    'automation': dict(self.config.automation_settings),
                    'ui_preferences': {
                        'theme': self.theme_manager.get_current_theme(),
                        'window_size': [self.width(), self.height()]
                    }
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "Export", f"Settings exported to:\n{file_path}")
                logging.info(f"Settings exported to: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export settings:\n{e}")
                logging.error(f"Failed to export settings: {e}")

    def _clear_all_data(self):
        """Clear all application data."""
        reply = QMessageBox.question(
            self,
            "Clear All Data",
            "Are you sure you want to clear all application data?\n"
            "This will reset all settings and clear all logs.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Clear logs
            self.log_display.clear()
            self.status_text.clear()

            # Reset settings to defaults
            # TODO: Implement data clearing logic

            QMessageBox.information(self, "Clear Data", "All data cleared successfully!")
            logging.info("All application data cleared")

    def _show_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self,
            "About TikTok Automation Tool",
            "TikTok Automation Tool v1.0.0\n\n"
            "Professional automation tool for educational purposes.\n\n"
            "⚠️ LEGAL DISCLAIMER:\n"
            "This tool is for educational purposes only.\n"
            "Users must comply with TikTok's Terms of Service\n"
            "and all applicable laws and regulations.\n\n"
            "Developed with PyQt5 and Python."
        )

    def _open_url(self, url: str):
        """Open URL in default browser."""
        from PyQt5.QtGui import QDesktopServices
        from PyQt5.QtCore import QUrl
        QDesktopServices.openUrl(QUrl(url))

    def closeEvent(self, event):
        """Handle window close event."""
        # Save window geometry
        self.config.set('ui', 'window_width', self.width())
        self.config.set('ui', 'window_height', self.height())
        self.config.set('ui', 'window_maximized', self.isMaximized())
        self.config.save()

        # Stop any running automation
        if self.stop_button.isEnabled():
            self._stop_automation()

        logging.info("Application closing")
        event.accept()

    # Automation signal handlers
    def _on_automation_progress(self, action_type: str, progress: int):
        """Handle automation progress update."""
        if action_type == "overall":
            self.overall_progress.setValue(progress)
        elif action_type == "likes":
            self.likes_progress.setValue(progress)
        elif action_type == "views":
            self.views_progress.setValue(progress)
        elif action_type == "comments":
            # Use likes progress bar for comments if no separate bar
            pass

    def _on_automation_status(self, status: str):
        """Handle automation status update."""
        self._add_status_message(status)

    def _on_automation_statistics(self, stats: dict):
        """Handle automation statistics update."""
        # Update statistics labels
        self.success_label.setText(str(stats.get('completed_tasks', 0)))
        self.failed_label.setText(str(stats.get('failed_tasks', 0)))

        # Update duration
        duration = stats.get('duration', 0)
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = int(duration % 60)
        self.duration_label.setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")

        # Update overall progress
        total_tasks = stats.get('total_tasks', 0)
        completed = stats.get('completed_tasks', 0) + stats.get('failed_tasks', 0)
        if total_tasks > 0:
            progress = int((completed / total_tasks) * 100)
            self.overall_progress.setValue(progress)

    def _on_automation_finished(self, success: bool, message: str):
        """Handle automation completion."""
        # Update UI state
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_indicator.setText("🔴 Stopped")

        # Add completion message
        self._add_status_message(f"✅ {message}")

        # Show completion dialog
        if success:
            QMessageBox.information(self, "Automation Complete", message)
        else:
            QMessageBox.warning(self, "Automation Error", message)
