"""
Proxy Manager for TikTok Automation Tool
Handles proxy configuration and rotation for anonymity.
"""

import random
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import requests
from urllib.parse import urlparse

@dataclass
class ProxyConfig:
    """Proxy configuration data."""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    proxy_type: str = "HTTP"  # HTTP, HTTPS, SOCKS4, SOCKS5

class ProxyManager:
    """Manages proxy configurations and rotation."""
    
    def __init__(self, config: Dict):
        """Initialize proxy manager.
        
        Args:
            config: Proxy configuration dictionary
        """
        self.config = config
        self.proxies: List[ProxyConfig] = []
        self.current_proxy_index = 0
        self.failed_proxies = set()
        
        # Load proxies from config
        self._load_proxies()
    
    def _load_proxies(self):
        """Load proxy configurations."""
        # Single proxy from config
        if self.config.get('host') and self.config.get('port'):
            proxy = ProxyConfig(
                host=self.config['host'],
                port=int(self.config['port']),
                username=self.config.get('username'),
                password=self.config.get('password'),
                proxy_type=self.config.get('type', 'HTTP')
            )
            self.proxies.append(proxy)
        
        # Load from proxy list file
        proxy_file = self.config.get('proxy_file')
        if proxy_file:
            self._load_proxy_file(proxy_file)
        
        logging.info(f"Loaded {len(self.proxies)} proxy configurations")
    
    def _load_proxy_file(self, file_path: str):
        """Load proxies from file.
        
        Args:
            file_path: Path to proxy file
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        proxy = self._parse_proxy_line(line)
                        if proxy:
                            self.proxies.append(proxy)
        
        except FileNotFoundError:
            logging.warning(f"Proxy file not found: {file_path}")
        except Exception as e:
            logging.error(f"Error loading proxy file: {e}")
    
    def _parse_proxy_line(self, line: str) -> Optional[ProxyConfig]:
        """Parse proxy line from file.
        
        Args:
            line: Proxy line in format "host:port" or "host:port:username:password"
            
        Returns:
            ProxyConfig object or None if invalid
        """
        try:
            parts = line.split(':')
            
            if len(parts) >= 2:
                host = parts[0]
                port = int(parts[1])
                username = parts[2] if len(parts) > 2 else None
                password = parts[3] if len(parts) > 3 else None
                
                return ProxyConfig(
                    host=host,
                    port=port,
                    username=username,
                    password=password,
                    proxy_type=self.config.get('type', 'HTTP')
                )
        
        except (ValueError, IndexError) as e:
            logging.warning(f"Invalid proxy line: {line} - {e}")
        
        return None
    
    def get_proxy_config(self) -> Optional[str]:
        """Get current proxy configuration string.
        
        Returns:
            Proxy configuration string for browser or None
        """
        if not self.proxies:
            return None
        
        proxy = self.get_current_proxy()
        if not proxy:
            return None
        
        # Format proxy string based on type
        if proxy.proxy_type.upper() in ['SOCKS4', 'SOCKS5']:
            protocol = proxy.proxy_type.lower()
        else:
            protocol = 'http'
        
        if proxy.username and proxy.password:
            return f"{protocol}://{proxy.username}:{proxy.password}@{proxy.host}:{proxy.port}"
        else:
            return f"{protocol}://{proxy.host}:{proxy.port}"
    
    def get_current_proxy(self) -> Optional[ProxyConfig]:
        """Get current proxy configuration.
        
        Returns:
            Current ProxyConfig or None
        """
        if not self.proxies:
            return None
        
        # Skip failed proxies
        attempts = 0
        while attempts < len(self.proxies):
            proxy = self.proxies[self.current_proxy_index]
            proxy_key = f"{proxy.host}:{proxy.port}"
            
            if proxy_key not in self.failed_proxies:
                return proxy
            
            self._rotate_proxy()
            attempts += 1
        
        # All proxies failed, clear failed list and try again
        if attempts >= len(self.proxies):
            logging.warning("All proxies failed, clearing failed list")
            self.failed_proxies.clear()
            return self.proxies[self.current_proxy_index] if self.proxies else None
        
        return None
    
    def _rotate_proxy(self):
        """Rotate to next proxy."""
        if self.proxies:
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
    
    def rotate_proxy(self) -> Optional[ProxyConfig]:
        """Manually rotate to next proxy.
        
        Returns:
            New current proxy or None
        """
        self._rotate_proxy()
        return self.get_current_proxy()
    
    def mark_proxy_failed(self, proxy: ProxyConfig):
        """Mark proxy as failed.
        
        Args:
            proxy: Failed proxy configuration
        """
        proxy_key = f"{proxy.host}:{proxy.port}"
        self.failed_proxies.add(proxy_key)
        
        logging.warning(f"Marked proxy as failed: {proxy_key}")
        
        # Rotate to next proxy
        self._rotate_proxy()
    
    def test_proxy(self, proxy: ProxyConfig, timeout: int = 10) -> bool:
        """Test if proxy is working.
        
        Args:
            proxy: Proxy configuration to test
            timeout: Request timeout in seconds
            
        Returns:
            True if proxy is working, False otherwise
        """
        try:
            # Create proxy dict for requests
            proxy_url = self.get_proxy_config()
            if not proxy_url:
                return False
            
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            
            # Test with a simple request
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxies,
                timeout=timeout
            )
            
            if response.status_code == 200:
                logging.info(f"Proxy test successful: {proxy.host}:{proxy.port}")
                return True
            else:
                logging.warning(f"Proxy test failed with status {response.status_code}: {proxy.host}:{proxy.port}")
                return False
        
        except Exception as e:
            logging.warning(f"Proxy test failed: {proxy.host}:{proxy.port} - {e}")
            return False
    
    def test_all_proxies(self) -> List[ProxyConfig]:
        """Test all proxies and return working ones.
        
        Returns:
            List of working proxy configurations
        """
        working_proxies = []
        
        for proxy in self.proxies:
            if self.test_proxy(proxy):
                working_proxies.append(proxy)
            else:
                self.mark_proxy_failed(proxy)
        
        logging.info(f"Found {len(working_proxies)} working proxies out of {len(self.proxies)}")
        return working_proxies
    
    def get_random_proxy(self) -> Optional[ProxyConfig]:
        """Get a random proxy configuration.
        
        Returns:
            Random ProxyConfig or None
        """
        if not self.proxies:
            return None
        
        # Filter out failed proxies
        available_proxies = [
            proxy for proxy in self.proxies
            if f"{proxy.host}:{proxy.port}" not in self.failed_proxies
        ]
        
        if not available_proxies:
            # Clear failed list if no proxies available
            self.failed_proxies.clear()
            available_proxies = self.proxies
        
        return random.choice(available_proxies) if available_proxies else None
    
    def get_proxy_stats(self) -> Dict:
        """Get proxy statistics.
        
        Returns:
            Dictionary containing proxy statistics
        """
        return {
            'total_proxies': len(self.proxies),
            'failed_proxies': len(self.failed_proxies),
            'working_proxies': len(self.proxies) - len(self.failed_proxies),
            'current_proxy_index': self.current_proxy_index,
            'current_proxy': f"{self.get_current_proxy().host}:{self.get_current_proxy().port}" if self.get_current_proxy() else None
        }
    
    def reset_failed_proxies(self):
        """Reset the failed proxies list."""
        self.failed_proxies.clear()
        logging.info("Reset failed proxies list")
    
    def add_proxy(self, host: str, port: int, username: str = None, password: str = None, proxy_type: str = "HTTP"):
        """Add a new proxy configuration.
        
        Args:
            host: Proxy host
            port: Proxy port
            username: Optional username
            password: Optional password
            proxy_type: Proxy type (HTTP, HTTPS, SOCKS4, SOCKS5)
        """
        proxy = ProxyConfig(
            host=host,
            port=port,
            username=username,
            password=password,
            proxy_type=proxy_type
        )
        
        self.proxies.append(proxy)
        logging.info(f"Added proxy: {host}:{port}")
    
    def remove_proxy(self, host: str, port: int):
        """Remove a proxy configuration.
        
        Args:
            host: Proxy host
            port: Proxy port
        """
        self.proxies = [
            proxy for proxy in self.proxies
            if not (proxy.host == host and proxy.port == port)
        ]
        
        # Remove from failed list too
        proxy_key = f"{host}:{port}"
        self.failed_proxies.discard(proxy_key)
        
        logging.info(f"Removed proxy: {host}:{port}")
    
    def is_enabled(self) -> bool:
        """Check if proxy is enabled.
        
        Returns:
            True if proxy is enabled and available
        """
        return len(self.proxies) > 0 and self.config.get('enabled', False)
