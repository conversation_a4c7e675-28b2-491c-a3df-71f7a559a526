<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 AI Voice Downloader</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .voice-section {
            background: rgba(255, 255, 255, 0.1);
            margin: 20px 0;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .voice-section h3 {
            margin-top: 0;
            color: #ffd700;
            font-size: 1.3em;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn.success {
            background: linear-gradient(45deg, #00b894, #00cec9);
        }
        
        .btn.download {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }
        
        select, textarea {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 14px;
            margin: 10px 0;
        }
        
        textarea {
            height: 120px;
            resize: vertical;
        }
        
        .timer-section {
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid #ffd700;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .timer-display {
            font-size: 2em;
            font-weight: bold;
            color: #ffd700;
            margin: 10px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .timer-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .timer-btn {
            padding: 8px 15px;
            font-size: 14px;
            border-radius: 20px;
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 10px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.success {
            background: rgba(0, 184, 148, 0.3);
            border: 1px solid #00b894;
        }
        
        .status.error {
            background: rgba(255, 107, 107, 0.3);
            border: 1px solid #ff6b6b;
        }
        
        .status.info {
            background: rgba(116, 185, 255, 0.3);
            border: 1px solid #74b9ff;
        }
        
        audio {
            width: 100%;
            margin: 15px 0;
            border-radius: 10px;
        }
        
        .duration-controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        .duration-btn {
            padding: 10px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .duration-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #ffd700;
        }
        
        .duration-btn.active {
            background: rgba(255, 215, 0, 0.3);
            border-color: #ffd700;
        }
        
        .custom-duration {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
        }
        
        .custom-duration input {
            width: 80px;
            padding: 8px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 AI Voice Downloader</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            Generate and download AI voices with precise timing control
        </p>
        
        <div id="status"></div>
        
        <!-- Voice Selection -->
        <div class="voice-section">
            <h3>🗣️ Choose AI Voice</h3>
            <select id="voiceSelect">
                <option value="">Loading voices...</option>
            </select>
        </div>
        
        <!-- Text Input -->
        <div class="voice-section">
            <h3>📝 Enter Text</h3>
            <textarea id="textInput" placeholder="Enter the text you want the AI to speak...">Hello! This is a test of the AI voice system. The speech should be clear and natural sounding.</textarea>
        </div>
        
        <!-- Duration Control -->
        <div class="voice-section">
            <h3>⏱️ Set Duration</h3>
            <p>Choose how long the audio should be:</p>
            
            <div class="duration-controls">
                <div class="duration-btn" data-duration="30">
                    <strong>30 seconds</strong><br>
                    <small>Short clip</small>
                </div>
                <div class="duration-btn active" data-duration="60">
                    <strong>60 seconds</strong><br>
                    <small>Standard</small>
                </div>
                <div class="duration-btn" data-duration="120">
                    <strong>2 minutes</strong><br>
                    <small>Long clip</small>
                </div>
            </div>
            
            <div class="custom-duration">
                <label>Custom duration:</label>
                <input type="number" id="customDuration" min="5" max="600" value="60">
                <span>seconds</span>
                <button class="btn timer-btn" id="setCustomBtn">Set</button>
            </div>
        </div>
        
        <!-- Timer Section -->
        <div class="timer-section">
            <h3>⏰ Recording Timer</h3>
            <div class="timer-display" id="timerDisplay">00:00</div>
            <div class="timer-controls">
                <button class="btn timer-btn" id="startTimerBtn">▶️ Start Timer</button>
                <button class="btn timer-btn" id="pauseTimerBtn" disabled>⏸️ Pause</button>
                <button class="btn timer-btn" id="resetTimerBtn">🔄 Reset</button>
            </div>
            <p id="timerStatus">Timer ready - Set your duration and start when ready</p>
        </div>
        
        <!-- Generate & Download -->
        <div class="voice-section">
            <h3>🎤 Generate AI Voice</h3>
            <button class="btn" id="generateBtn" disabled>🚀 Generate AI Voice</button>
            <button class="btn download" id="downloadBtn" style="display: none;">📥 Download Audio</button>
            
            <audio id="audioPlayer" controls style="display: none;"></audio>
        </div>
    </div>

    <script>
        class AIVoiceDownloader {
            constructor() {
                this.apiBase = 'http://localhost:5000/api';
                this.voices = [];
                this.selectedVoice = null;
                this.currentDuration = 60;
                this.timer = {
                    seconds: 0,
                    interval: null,
                    isRunning: false,
                    targetDuration: 60
                };
                this.lastAudioUrl = null;
                
                this.initializeEventListeners();
                this.loadVoices();
                this.updateTimerDisplay();
            }
            
            initializeEventListeners() {
                // Voice selection
                document.getElementById('voiceSelect').addEventListener('change', (e) => {
                    this.selectVoice(e.target.value);
                });
                
                // Duration buttons
                document.querySelectorAll('.duration-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.setDuration(parseInt(e.currentTarget.dataset.duration));
                    });
                });
                
                // Custom duration
                document.getElementById('setCustomBtn').addEventListener('click', () => {
                    const customDuration = parseInt(document.getElementById('customDuration').value);
                    if (customDuration >= 5 && customDuration <= 600) {
                        this.setDuration(customDuration);
                    } else {
                        this.showStatus('Duration must be between 5 and 600 seconds', 'error');
                    }
                });
                
                // Timer controls
                document.getElementById('startTimerBtn').addEventListener('click', () => {
                    this.startTimer();
                });
                
                document.getElementById('pauseTimerBtn').addEventListener('click', () => {
                    this.pauseTimer();
                });
                
                document.getElementById('resetTimerBtn').addEventListener('click', () => {
                    this.resetTimer();
                });
                
                // Text input monitoring
                document.getElementById('textInput').addEventListener('input', () => {
                    this.checkGenerateButton();
                });
                
                // Generate button
                document.getElementById('generateBtn').addEventListener('click', () => {
                    this.generateVoice();
                });
                
                // Download button
                document.getElementById('downloadBtn').addEventListener('click', () => {
                    this.downloadAudio();
                });
            }
            
            async loadVoices() {
                try {
                    this.showStatus('Loading AI voices...', 'info');
                    
                    const response = await fetch(`${this.apiBase}/tts/voices`);
                    const result = await response.json();
                    
                    if (response.ok) {
                        this.voices = result.voices;
                        this.populateVoiceSelect();
                        this.showStatus(`${this.voices.length} AI voices loaded!`, 'success');
                    } else {
                        this.showStatus(`Error: ${result.error}`, 'error');
                    }
                } catch (error) {
                    this.showStatus(`Connection error: ${error.message}`, 'error');
                }
            }
            
            populateVoiceSelect() {
                const select = document.getElementById('voiceSelect');
                select.innerHTML = '<option value="">Choose an AI voice...</option>';
                
                this.voices.forEach(voice => {
                    const option = document.createElement('option');
                    option.value = voice.name;
                    option.textContent = `${voice.name} (${voice.description || voice.language})`;
                    select.appendChild(option);
                });
            }
            
            selectVoice(voiceName) {
                this.selectedVoice = this.voices.find(v => v.name === voiceName);
                if (this.selectedVoice) {
                    this.showStatus(`Voice selected: ${this.selectedVoice.name}`, 'success');
                    this.checkGenerateButton();
                }
            }
            
            setDuration(duration) {
                this.currentDuration = duration;
                this.timer.targetDuration = duration;
                
                // Update UI
                document.querySelectorAll('.duration-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                const activeBtn = document.querySelector(`[data-duration="${duration}"]`);
                if (activeBtn) {
                    activeBtn.classList.add('active');
                } else {
                    // Custom duration
                    document.getElementById('customDuration').value = duration;
                }
                
                this.resetTimer();
                this.showStatus(`Duration set to ${duration} seconds`, 'info');
            }
            
            startTimer() {
                if (this.timer.isRunning) return;
                
                this.timer.isRunning = true;
                document.getElementById('startTimerBtn').disabled = true;
                document.getElementById('pauseTimerBtn').disabled = false;
                document.getElementById('timerStatus').textContent = 'Timer running...';
                
                this.timer.interval = setInterval(() => {
                    this.timer.seconds++;
                    this.updateTimerDisplay();
                    
                    // Check if target duration reached
                    if (this.timer.seconds >= this.timer.targetDuration) {
                        this.pauseTimer();
                        this.showStatus(`⏰ Target duration (${this.timer.targetDuration}s) reached!`, 'success');
                        document.getElementById('timerStatus').textContent = `Target duration reached! (${this.timer.targetDuration}s)`;
                    }
                }, 1000);
            }
            
            pauseTimer() {
                this.timer.isRunning = false;
                clearInterval(this.timer.interval);
                document.getElementById('startTimerBtn').disabled = false;
                document.getElementById('pauseTimerBtn').disabled = true;
                document.getElementById('timerStatus').textContent = 'Timer paused';
            }
            
            resetTimer() {
                this.pauseTimer();
                this.timer.seconds = 0;
                this.updateTimerDisplay();
                document.getElementById('timerStatus').textContent = `Timer ready - Target: ${this.timer.targetDuration}s`;
            }
            
            updateTimerDisplay() {
                const minutes = Math.floor(this.timer.seconds / 60);
                const seconds = this.timer.seconds % 60;
                const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                document.getElementById('timerDisplay').textContent = display;
                
                // Color coding
                const timerDisplay = document.getElementById('timerDisplay');
                if (this.timer.seconds >= this.timer.targetDuration) {
                    timerDisplay.style.color = '#ff6b6b';  // Red when over target
                } else if (this.timer.seconds >= this.timer.targetDuration * 0.8) {
                    timerDisplay.style.color = '#fdcb6e';  // Orange when close
                } else {
                    timerDisplay.style.color = '#ffd700';  // Gold when good
                }
            }
            
            checkGenerateButton() {
                const hasVoice = this.selectedVoice !== null;
                const hasText = document.getElementById('textInput').value.trim().length > 0;
                
                const generateBtn = document.getElementById('generateBtn');
                if (hasVoice && hasText) {
                    generateBtn.disabled = false;
                    generateBtn.classList.add('success');
                } else {
                    generateBtn.disabled = true;
                    generateBtn.classList.remove('success');
                }
            }
            
            async generateVoice() {
                if (!this.selectedVoice) {
                    this.showStatus('Please select a voice first!', 'error');
                    return;
                }
                
                const text = document.getElementById('textInput').value.trim();
                if (!text) {
                    this.showStatus('Please enter text!', 'error');
                    return;
                }
                
                try {
                    this.showStatus('Generating AI voice...', 'info');
                    
                    const response = await fetch(`${this.apiBase}/tts/preview`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            voice_name: this.selectedVoice.name
                        })
                    });
                    
                    if (response.ok) {
                        const audioBlob = await response.blob();
                        const audioUrl = URL.createObjectURL(audioBlob);
                        
                        const audioElement = document.getElementById('audioPlayer');
                        audioElement.src = audioUrl;
                        audioElement.style.display = 'block';
                        audioElement.play();
                        
                        this.lastAudioUrl = audioUrl;
                        document.getElementById('downloadBtn').style.display = 'inline-block';
                        
                        this.showStatus('✅ AI voice generated! Play audio above.', 'success');
                    } else {
                        const error = await response.json();
                        this.showStatus(`TTS error: ${error.error}`, 'error');
                    }
                } catch (error) {
                    this.showStatus(`Error: ${error.message}`, 'error');
                }
            }
            
            downloadAudio() {
                if (this.lastAudioUrl) {
                    const link = document.createElement('a');
                    link.href = this.lastAudioUrl;
                    link.download = `ai_voice_${this.selectedVoice.name}_${Date.now()}.wav`;
                    link.click();
                    
                    this.showStatus('Download started!', 'success');
                }
            }
            
            showStatus(message, type) {
                const statusDiv = document.getElementById('status');
                statusDiv.className = `status ${type}`;
                statusDiv.textContent = message;
                statusDiv.style.display = 'block';
                
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new AIVoiceDownloader();
        });
    </script>
</body>
</html>
