@echo off
title TikTok Automation Tool - EXE Builder
color 0A
echo.
echo  ████████╗██╗██╗  ██╗████████╗ ██████╗ ██╗  ██╗
echo  ╚══██╔══╝██║██║ ██╔╝╚══██╔══╝██╔═══██╗██║ ██╔╝
echo     ██║   ██║█████╔╝    ██║   ██║   ██║█████╔╝
echo     ██║   ██║██╔═██╗    ██║   ██║   ██║██╔═██╗
echo     ██║   ██║██║  ██╗   ██║   ╚██████╔╝██║  ██╗
echo     ╚═╝   ╚═╝╚═╝  ╚═╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝
echo.
echo            AUTOMATION TOOL - EXE BUILDER
echo ========================================================
echo.

echo Step 1: Installing PyInstaller...
python -m pip install --upgrade pip
python -m pip install pyinstaller
if errorlevel 1 (
    echo Trying alternative installation method...
    pip install pyinstaller
    if errorlevel 1 (
        echo ERROR: Failed to install PyInstaller
        echo Make sure Python and pip are properly installed
        pause
        exit /b 1
    )
)

echo.
echo Step 2: Installing dependencies...
pip install PyQt5 selenium requests cryptography bcrypt webdriver-manager psutil loguru
if errorlevel 1 (
    echo WARNING: Some dependencies may have failed to install
)

echo.
echo Step 3: Cleaning previous builds...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo.
echo Step 4: Building executable with COMPLETE IMPORTS...
echo This may take 5-10 minutes...
echo.
echo Building with ALL required modules (no more missing imports!)...
python -m PyInstaller ^
--onefile ^
--windowed ^
--name=TikTokAutomationTool ^
--add-data="src;src" ^
--add-data="assets;assets" ^
--add-data="config;config" ^
--hidden-import=PyQt5 ^
--hidden-import=PyQt5.QtCore ^
--hidden-import=PyQt5.QtGui ^
--hidden-import=PyQt5.QtWidgets ^
--hidden-import=PyQt5.QtTest ^
--hidden-import=selenium ^
--hidden-import=selenium.webdriver ^
--hidden-import=selenium.webdriver.chrome ^
--hidden-import=selenium.webdriver.chrome.service ^
--hidden-import=selenium.webdriver.chrome.options ^
--hidden-import=selenium.webdriver.common ^
--hidden-import=selenium.webdriver.common.by ^
--hidden-import=selenium.webdriver.support ^
--hidden-import=selenium.webdriver.support.ui ^
--hidden-import=selenium.webdriver.support.expected_conditions ^
--hidden-import=selenium.common ^
--hidden-import=selenium.common.exceptions ^
--hidden-import=requests ^
--hidden-import=requests.adapters ^
--hidden-import=requests.auth ^
--hidden-import=requests.cookies ^
--hidden-import=requests.exceptions ^
--hidden-import=cryptography ^
--hidden-import=cryptography.fernet ^
--hidden-import=cryptography.hazmat ^
--hidden-import=cryptography.hazmat.primitives ^
--hidden-import=cryptography.hazmat.primitives.hashes ^
--hidden-import=cryptography.hazmat.primitives.kdf ^
--hidden-import=cryptography.hazmat.primitives.kdf.pbkdf2 ^
--hidden-import=cryptography.hazmat.backends ^
--hidden-import=cryptography.hazmat.backends.openssl ^
--hidden-import=bcrypt ^
--hidden-import=webdriver_manager ^
--hidden-import=webdriver_manager.chrome ^
--hidden-import=webdriver_manager.core ^
--hidden-import=psutil ^
--hidden-import=loguru ^
--hidden-import=configparser ^
--hidden-import=pathlib ^
--hidden-import=json ^
--hidden-import=logging ^
--hidden-import=logging.handlers ^
--hidden-import=logging.config ^
--hidden-import=threading ^
--hidden-import=queue ^
--hidden-import=time ^
--hidden-import=os ^
--hidden-import=sys ^
--hidden-import=base64 ^
--hidden-import=hashlib ^
--hidden-import=secrets ^
--hidden-import=functools ^
--hidden-import=typing ^
--hidden-import=dataclasses ^
--hidden-import=enum ^
--hidden-import=abc ^
--hidden-import=datetime ^
--hidden-import=traceback ^
--hidden-import=urllib ^
--hidden-import=urllib.parse ^
--hidden-import=urllib.request ^
--hidden-import=urllib.error ^
--hidden-import=http ^
--hidden-import=http.client ^
--hidden-import=http.server ^
--hidden-import=random ^
--hidden-import=re ^
--hidden-import=socket ^
--hidden-import=ssl ^
--hidden-import=platform ^
--hidden-import=subprocess ^
--hidden-import=shutil ^
--hidden-import=tempfile ^
--hidden-import=zipfile ^
--hidden-import=collections ^
--hidden-import=itertools ^
--hidden-import=copy ^
--hidden-import=pickle ^
--hidden-import=struct ^
--hidden-import=math ^
--hidden-import=statistics ^
--hidden-import=decimal ^
--hidden-import=fractions ^
--hidden-import=operator ^
--hidden-import=weakref ^
--hidden-import=gc ^
--hidden-import=inspect ^
--hidden-import=importlib ^
--hidden-import=importlib.util ^
--hidden-import=pkgutil ^
--hidden-import=warnings ^
--hidden-import=contextlib ^
--hidden-import=atexit ^
--hidden-import=signal ^
--hidden-import=getpass ^
--hidden-import=locale ^
--hidden-import=codecs ^
--hidden-import=encodings ^
--hidden-import=encodings.utf_8 ^
--hidden-import=encodings.latin_1 ^
--hidden-import=encodings.ascii ^
--hidden-import=io ^
--hidden-import=mmap ^
--hidden-import=select ^
--hidden-import=errno ^
--hidden-import=stat ^
--hidden-import=glob ^
--hidden-import=fnmatch ^
--hidden-import=linecache ^
--hidden-import=tokenize ^
--hidden-import=keyword ^
--hidden-import=ast ^
--hidden-import=dis ^
--hidden-import=code ^
--hidden-import=codeop ^
--hidden-import=py_compile ^
--hidden-import=compileall ^
--hidden-import=zipimport ^
--hidden-import=runpy ^
--hidden-import=types ^
--hidden-import=builtins ^
--clean ^
--noconfirm ^
main.py

if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo Step 5: Checking result...
if exist "dist\TikTokAutomationTool.exe" (
    echo SUCCESS: Executable created!
    echo Location: dist\TikTokAutomationTool.exe
    dir "dist\TikTokAutomationTool.exe"
) else (
    echo ERROR: Executable not found
    pause
    exit /b 1
)

echo.
echo ========================================
echo   BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Executable location: dist\TikTokAutomationTool.exe
echo.
echo You can now:
echo 1. Test the executable
echo 2. Distribute the exe file
echo 3. Create a full release package
echo.
pause
