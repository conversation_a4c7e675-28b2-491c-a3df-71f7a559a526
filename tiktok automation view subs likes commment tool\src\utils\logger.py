"""
Logging Configuration for TikTok Automation Tool
Professional logging setup with file rotation and multiple handlers.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional

def setup_logging(log_dir: Path, level: str = "INFO", max_bytes: int = 10485760, backup_count: int = 5):
    """Setup comprehensive logging configuration.
    
    Args:
        log_dir: Directory to store log files
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        max_bytes: Maximum size of log file before rotation (default: 10MB)
        backup_count: Number of backup files to keep
    """
    
    # Create log directory if it doesn't exist
    log_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        fmt='%(asctime)s | %(levelname)-8s | %(name)-20s | %(funcName)-15s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        fmt='%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # File handler with rotation
    log_file = log_dir / f"tiktok_automation_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(simple_formatter)
    
    # Error file handler (separate file for errors)
    error_file = log_dir / f"errors_{datetime.now().strftime('%Y%m%d')}.log"
    error_handler = logging.handlers.RotatingFileHandler(
        filename=error_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    
    # Add handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(error_handler)
    
    # Log startup message
    logging.info("="*80)
    logging.info("TikTok Automation Tool - Logging System Initialized")
    logging.info(f"Log Level: {level}")
    logging.info(f"Log Directory: {log_dir}")
    logging.info(f"Main Log File: {log_file}")
    logging.info(f"Error Log File: {error_file}")
    logging.info("="*80)

class AutomationLogger:
    """Specialized logger for automation activities."""
    
    def __init__(self, log_dir: Path):
        """Initialize automation logger.
        
        Args:
            log_dir: Directory to store automation logs
        """
        self.log_dir = log_dir
        self.automation_log_file = log_dir / f"automation_{datetime.now().strftime('%Y%m%d')}.log"
        
        # Create automation logger
        self.logger = logging.getLogger('automation')
        self.logger.setLevel(logging.INFO)
        
        # Create handler if not exists
        if not self.logger.handlers:
            handler = logging.handlers.RotatingFileHandler(
                filename=self.automation_log_file,
                maxBytes=10485760,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            
            formatter = logging.Formatter(
                fmt='%(asctime)s | %(levelname)-8s | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def log_action(self, action: str, target: str, status: str, details: Optional[str] = None):
        """Log automation action.
        
        Args:
            action: Action performed (like, follow, comment, etc.)
            target: Target URL or username
            status: Status (success, failed, skipped)
            details: Additional details
        """
        message = f"ACTION: {action} | TARGET: {target} | STATUS: {status}"
        if details:
            message += f" | DETAILS: {details}"
        
        if status.lower() == 'success':
            self.logger.info(message)
        elif status.lower() == 'failed':
            self.logger.error(message)
        else:
            self.logger.warning(message)
    
    def log_session_start(self, session_type: str, target_count: int):
        """Log automation session start.
        
        Args:
            session_type: Type of automation session
            target_count: Number of targets to process
        """
        self.logger.info(f"SESSION START: {session_type} | TARGETS: {target_count}")
    
    def log_session_end(self, session_type: str, success_count: int, failed_count: int, duration: float):
        """Log automation session end.
        
        Args:
            session_type: Type of automation session
            success_count: Number of successful actions
            failed_count: Number of failed actions
            duration: Session duration in seconds
        """
        self.logger.info(
            f"SESSION END: {session_type} | "
            f"SUCCESS: {success_count} | "
            f"FAILED: {failed_count} | "
            f"DURATION: {duration:.2f}s"
        )
    
    def log_error(self, error: str, context: Optional[str] = None):
        """Log automation error.
        
        Args:
            error: Error message
            context: Additional context
        """
        message = f"ERROR: {error}"
        if context:
            message += f" | CONTEXT: {context}"
        
        self.logger.error(message)
    
    def log_warning(self, warning: str, context: Optional[str] = None):
        """Log automation warning.
        
        Args:
            warning: Warning message
            context: Additional context
        """
        message = f"WARNING: {warning}"
        if context:
            message += f" | CONTEXT: {context}"
        
        self.logger.warning(message)

class PerformanceLogger:
    """Logger for performance metrics."""
    
    def __init__(self, log_dir: Path):
        """Initialize performance logger.
        
        Args:
            log_dir: Directory to store performance logs
        """
        self.log_dir = log_dir
        self.performance_log_file = log_dir / f"performance_{datetime.now().strftime('%Y%m%d')}.log"
        
        # Create performance logger
        self.logger = logging.getLogger('performance')
        self.logger.setLevel(logging.INFO)
        
        # Create handler if not exists
        if not self.logger.handlers:
            handler = logging.handlers.RotatingFileHandler(
                filename=self.performance_log_file,
                maxBytes=5242880,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            
            formatter = logging.Formatter(
                fmt='%(asctime)s | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def log_timing(self, operation: str, duration: float, details: Optional[str] = None):
        """Log operation timing.
        
        Args:
            operation: Operation name
            duration: Duration in seconds
            details: Additional details
        """
        message = f"TIMING: {operation} | DURATION: {duration:.3f}s"
        if details:
            message += f" | DETAILS: {details}"
        
        self.logger.info(message)
    
    def log_memory_usage(self, operation: str, memory_mb: float):
        """Log memory usage.
        
        Args:
            operation: Operation name
            memory_mb: Memory usage in MB
        """
        self.logger.info(f"MEMORY: {operation} | USAGE: {memory_mb:.2f}MB")
    
    def log_resource_usage(self, cpu_percent: float, memory_mb: float, threads: int):
        """Log system resource usage.
        
        Args:
            cpu_percent: CPU usage percentage
            memory_mb: Memory usage in MB
            threads: Number of active threads
        """
        self.logger.info(
            f"RESOURCES: CPU: {cpu_percent:.1f}% | "
            f"MEMORY: {memory_mb:.2f}MB | "
            f"THREADS: {threads}"
        )
