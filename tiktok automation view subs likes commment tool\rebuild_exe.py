#!/usr/bin/env python3
"""
Rebuild EXE with fixed imports
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def rebuild_exe():
    """Rebuild the executable with all required imports."""
    print("🔧 Rebuilding TikTok Automation Tool with fixed imports...")
    print("=" * 60)
    
    try:
        # Step 1: Clean previous build
        print("\n🧹 Cleaning previous build...")
        for dir_name in ['build', 'dist']:
            if Path(dir_name).exists():
                shutil.rmtree(dir_name)
                print(f"   Removed {dir_name}/")
        
        # Step 2: Install/upgrade PyInstaller
        print("\n📦 Installing PyInstaller...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '--upgrade', 'pyinstaller'
        ], check=True)
        print("✅ PyInstaller ready")
        
        # Step 3: Build with all required imports
        print("\n🔨 Building executable with fixed imports...")
        print("This will take 5-10 minutes...")
        
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--windowed',
            '--name=TikTokAutomationTool',
            '--add-data=src;src',
            '--add-data=assets;assets', 
            '--add-data=config;config',
            
            # PyQt5 imports
            '--hidden-import=PyQt5.QtCore',
            '--hidden-import=PyQt5.QtGui',
            '--hidden-import=PyQt5.QtWidgets',
            '--hidden-import=PyQt5.QtTest',
            
            # Standard library imports (the missing ones!)
            '--hidden-import=configparser',
            '--hidden-import=pathlib',
            '--hidden-import=json',
            '--hidden-import=logging',
            '--hidden-import=threading',
            '--hidden-import=queue',
            '--hidden-import=time',
            '--hidden-import=os',
            '--hidden-import=sys',
            '--hidden-import=base64',
            '--hidden-import=hashlib',
            '--hidden-import=secrets',
            '--hidden-import=functools',
            '--hidden-import=typing',
            '--hidden-import=dataclasses',
            '--hidden-import=enum',
            '--hidden-import=abc',
            
            # Third party imports
            '--hidden-import=selenium',
            '--hidden-import=selenium.webdriver',
            '--hidden-import=selenium.webdriver.chrome',
            '--hidden-import=selenium.webdriver.common.by',
            '--hidden-import=selenium.webdriver.support',
            '--hidden-import=selenium.webdriver.support.ui',
            '--hidden-import=selenium.webdriver.support.expected_conditions',
            '--hidden-import=selenium.common.exceptions',
            '--hidden-import=requests',
            '--hidden-import=cryptography',
            '--hidden-import=cryptography.fernet',
            '--hidden-import=bcrypt',
            '--hidden-import=webdriver_manager',
            '--hidden-import=webdriver_manager.chrome',
            '--hidden-import=psutil',
            '--hidden-import=loguru',
            
            # Build options
            '--clean',
            '--noconfirm',
            
            # Main script
            'main.py'
        ]
        
        print("Running PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build completed successfully!")
            
            # Check result
            exe_path = Path('dist/TikTokAutomationTool.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"\n🎉 SUCCESS!")
                print(f"📁 Executable: {exe_path.absolute()}")
                print(f"📊 Size: {size_mb:.1f} MB")
                print(f"\n✅ Fixed the configparser import issue!")
                print(f"✅ Added all missing standard library imports!")
                print(f"\n🚀 Your TikTok Automation Tool is ready to use!")
                return True
            else:
                print("❌ Executable not found after build")
                return False
        else:
            print("❌ Build failed")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TikTok Automation Tool - Rebuild with Fixes")
    success = rebuild_exe()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 REBUILD SUCCESSFUL!")
        print("=" * 60)
        print("The configparser import issue has been fixed!")
        print("Try running TikTokAutomationTool.exe now!")
    else:
        print("\n❌ Rebuild failed. Check error messages above.")
    
    input("\nPress Enter to exit...")
