
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), selenium.common.exceptions (top-level), selenium.webdriver.chrome.service (top-level), selenium.types (top-level), selenium.webdriver.chromium.service (top-level), selenium.webdriver.common.service (top-level), http.client (top-level), selenium.webdriver.common.utils (top-level), typing_extensions (top-level), asyncio.base_events (top-level), asyncio.coroutines (top-level), socks (optional), selenium.webdriver.common.bidi.common (top-level), selenium.webdriver.edge.service (top-level), selenium.webdriver.firefox.service (top-level), selenium.webdriver.ie.service (top-level), selenium.webdriver.safari.service (top-level), selenium.webdriver.webkitgtk.service (top-level), selenium.webdriver.wpewebkit.service (top-level), selenium.webdriver.support.expected_conditions (top-level), requests.compat (top-level), cryptography.utils (top-level), cryptography.x509.name (top-level), cryptography.x509.base (top-level), cryptography.hazmat.bindings.openssl.binding (top-level), cryptography.x509.extensions (top-level), cryptography.fernet (top-level), configparser (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), loguru._logger (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.current_process - imported by multiprocessing (top-level), loguru._logger (top-level)
missing module named readline - imported by code (delayed, conditional, optional)
missing module named termios - imported by getpass (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed, optional), psutil (optional), http.server (delayed, optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional)
missing module named fcntl - imported by subprocess (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named aiocontextvars - imported by loguru._contextvars (delayed, conditional)
missing module named exceptiongroup - imported by loguru._better_exceptions (conditional, optional)
missing module named IPython - imported by dotenv.ipython (top-level), loguru._colorama (delayed, conditional, optional)
missing module named ipykernel - imported by loguru._colorama (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named collections.Callable - imported by collections (optional), socks (optional)
missing module named chardet - imported by requests (optional)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'python_socks.sync' - imported by websocket._http (optional)
missing module named 'python_socks._types' - imported by websocket._http (optional)
missing module named python_socks - imported by websocket._http (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named _winreg - imported by selenium.webdriver.firefox.firefox_binary (delayed, optional)
missing module named 'utils.error_handler' - imported by C:\Users\<USER>\Documents\augment-projects\mijn projecten\tiktok automation view subs likes commment tool\main.py (top-level)
missing module named 'utils.security' - imported by C:\Users\<USER>\Documents\augment-projects\mijn projecten\tiktok automation view subs likes commment tool\main.py (top-level)
missing module named 'utils.theme' - imported by C:\Users\<USER>\Documents\augment-projects\mijn projecten\tiktok automation view subs likes commment tool\main.py (top-level)
missing module named 'utils.config' - imported by C:\Users\<USER>\Documents\augment-projects\mijn projecten\tiktok automation view subs likes commment tool\main.py (top-level)
missing module named utils - imported by C:\Users\<USER>\Documents\augment-projects\mijn projecten\tiktok automation view subs likes commment tool\main.py (top-level)
missing module named 'ui.splash_screen' - imported by C:\Users\<USER>\Documents\augment-projects\mijn projecten\tiktok automation view subs likes commment tool\main.py (top-level)
missing module named 'ui.login_dialog' - imported by C:\Users\<USER>\Documents\augment-projects\mijn projecten\tiktok automation view subs likes commment tool\main.py (top-level)
missing module named ui - imported by C:\Users\<USER>\Documents\augment-projects\mijn projecten\tiktok automation view subs likes commment tool\main.py (top-level)
