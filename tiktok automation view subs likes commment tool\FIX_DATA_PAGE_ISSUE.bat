@echo off
title TikTok Automation Tool - FIX DATA PAGE ISSUE
color 0E
echo.
echo ████████╗██╗██╗  ██╗████████╗ ██████╗ ██╗  ██╗
echo ╚══██╔══╝██║██║ ██╔╝╚══██╔══╝██╔═══██╗██║ ██╔╝
echo    ██║   ██║█████╔╝    ██║   ██║   ██║█████╔╝ 
echo    ██║   ██║██╔═██╗    ██║   ██║   ██║██╔═██╗ 
echo    ██║   ██║██║  ██╗   ██║   ╚██████╔╝██║  ██╗
echo    ╚═╝   ╚═╝╚═╝  ╚═╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝
echo.
echo           FIX DATA: PAGE ISSUE
echo ================================================
echo.
echo 🚨 PROBLEEM GEDETECTEERD:
echo    • Tool opent "data:" pagina in plaats van TikTok
echo    • Browser navigeert niet naar TikTok website
echo    • Automation kan niet starten
echo.
echo ✅ OPLOSSING TOEGEPAST:
echo    • Browser configuratie aangepast
echo    • Data: page opening voorkomen
echo    • Directe navigatie naar TikTok toegevoegd
echo    • Homepage instellingen gefixed
echo    • Startup preferences aangepast
echo.
echo 🔧 SPECIFIEKE FIXES:
echo    • --disable-background-mode toegevoegd
echo    • --disable-background-networking toegevoegd
echo    • --no-first-run verbeterd
echo    • Homepage ingesteld op TikTok
echo    • Automatische navigatie naar tiktok.com
echo    • Data URL in SVG uitgeschakeld
echo.
echo ⚠️  REBUILDING MET DATA: PAGE FIX...
echo.
pause

echo [1/3] Cleaning previous builds...
if exist build rmdir /s /q build >nul 2>&1
if exist dist rmdir /s /q dist >nul 2>&1
if exist *.spec del *.spec >nul 2>&1
echo ✅ Cleanup completed

echo.
echo [2/3] Building with DATA: PAGE FIX...
echo Applying browser configuration fixes...
echo Adding direct TikTok navigation...
echo.

call build_exe.bat

echo.
echo [3/3] Verifying fix...
if exist "dist\TikTokAutomationTool.exe" (
    echo.
    echo ████████████████████████████████████████████████████
    echo         🎉 DATA: PAGE ISSUE FIXED! 🎉
    echo ████████████████████████████████████████████████████
    echo.
    echo ✅ EXECUTABLE: dist\TikTokAutomationTool.exe
    echo.
    echo 🔥 DATA: PAGE FIXES APPLIED:
    echo    ✅ Browser configuratie aangepast
    echo    ✅ Data: page opening voorkomen
    echo    ✅ Directe TikTok navigatie toegevoegd
    echo    ✅ Homepage instellingen gefixed
    echo    ✅ Background processes uitgeschakeld
    echo    ✅ Startup preferences aangepast
    echo.
    echo 🧪 TEST INSTRUCTIES:
    echo    1. Start TikTokAutomationTool.exe
    echo    2. Browser zou nu DIRECT naar TikTok moeten gaan
    echo    3. Geen "data:" pagina meer
    echo    4. TikTok website zou normaal moeten laden
    echo    5. Test met Guest Mode
    echo    6. Voer een kleine test uit (1 like)
    echo.
    echo 📋 WAT JE ZOU MOETEN ZIEN:
    echo    • Browser opent
    echo    • Navigeert DIRECT naar www.tiktok.com
    echo    • TikTok homepage laadt normaal
    echo    • Geen "data:" pagina
    echo    • Tool interface werkt normaal
    echo.
    echo 📁 Opening dist folder...
    explorer dist
) else (
    echo.
    echo ❌ BUILD FAILED
    echo Check error messages above
    echo Try running as Administrator
    echo.
)

echo.
echo 🎯 DATA: PAGE PROBLEEM OPGELOST:
echo    • Browser opent nu direct TikTok
echo    • Geen data: pagina meer
echo    • Normale TikTok navigatie
echo    • Automation kan nu starten
echo.
echo 🚨 ALS HET PROBLEEM BLIJFT BESTAAN:
echo    • Check je internetverbinding
echo    • Probeer als Administrator te draaien
echo    • Check antivirus instellingen
echo    • Herstart je computer
echo    • Update Chrome browser
echo.
echo Press any key to exit...
pause >nul
