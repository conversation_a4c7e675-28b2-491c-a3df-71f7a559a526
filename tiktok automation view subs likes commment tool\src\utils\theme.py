"""
Theme Manager for TikTok Automation Tool
Professional TikTok-inspired dark and light themes with modern styling.
"""

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal
from typing import Dict, Any

class ThemeManager(QObject):
    """Manages application themes and styling."""
    
    theme_changed = pyqtSignal(str)  # Signal emitted when theme changes
    
    def __init__(self, app: QApplication):
        """Initialize theme manager.
        
        Args:
            app: QApplication instance
        """
        super().__init__()
        self.app = app
        self.current_theme = "dark"
        
        # Define theme configurations
        self.themes = {
            "dark": self._get_dark_theme(),
            "light": self._get_light_theme()
        }
    
    def _get_dark_theme(self) -> Dict[str, Any]:
        """Get dark theme configuration."""
        return {
            "name": "TikTok Dark",
            "colors": {
                "primary": "#FF0050",      # TikTok Red
                "secondary": "#25F4EE",    # TikT<PERSON>an
                "background": "#000000",   # Pure Black
                "surface": "#161823",      # Dark Gray
                "card": "#1F1F23",         # Card Background
                "text_primary": "#FFFFFF", # White Text
                "text_secondary": "#A8A8B3", # Gray Text
                "border": "#2F2F35",       # Border Color
                "success": "#00D084",      # Success Green
                "warning": "#FFB800",      # Warning Orange
                "error": "#FF3040",        # Error Red
                "accent": "#8A2BE2"        # Purple Accent
            },
            "stylesheet": self._get_dark_stylesheet()
        }
    
    def _get_light_theme(self) -> Dict[str, Any]:
        """Get light theme configuration."""
        return {
            "name": "TikTok Light",
            "colors": {
                "primary": "#FF0050",      # TikTok Red
                "secondary": "#25F4EE",    # TikTok Cyan
                "background": "#FFFFFF",   # Pure White
                "surface": "#F8F8F8",      # Light Gray
                "card": "#FFFFFF",         # Card Background
                "text_primary": "#161823", # Dark Text
                "text_secondary": "#6B7280", # Gray Text
                "border": "#E5E7EB",       # Border Color
                "success": "#00D084",      # Success Green
                "warning": "#FFB800",      # Warning Orange
                "error": "#FF3040",        # Error Red
                "accent": "#8A2BE2"        # Purple Accent
            },
            "stylesheet": self._get_light_stylesheet()
        }
    
    def _get_dark_stylesheet(self) -> str:
        """Get dark theme stylesheet."""
        return """
        /* Main Window */
        QMainWindow {
            background-color: #000000;
            color: #FFFFFF;
        }
        
        /* Central Widget */
        QWidget {
            background-color: #000000;
            color: #FFFFFF;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        /* Buttons */
        QPushButton {
            background-color: #FF0050;
            color: #FFFFFF;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            min-height: 20px;
        }
        
        QPushButton:hover {
            background-color: #E6004A;
            transform: translateY(-1px);
        }
        
        QPushButton:pressed {
            background-color: #CC0042;
        }
        
        QPushButton:disabled {
            background-color: #2F2F35;
            color: #A8A8B3;
        }
        
        /* Secondary Buttons */
        QPushButton[class="secondary"] {
            background-color: #25F4EE;
            color: #000000;
        }
        
        QPushButton[class="secondary"]:hover {
            background-color: #1FD4C9;
        }
        
        /* Input Fields */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #161823;
            border: 2px solid #2F2F35;
            border-radius: 8px;
            padding: 12px;
            color: #FFFFFF;
            font-size: 14px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #FF0050;
            outline: none;
        }
        
        /* ComboBox */
        QComboBox {
            background-color: #161823;
            border: 2px solid #2F2F35;
            border-radius: 8px;
            padding: 12px;
            color: #FFFFFF;
            font-size: 14px;
            min-width: 120px;
        }
        
        QComboBox:hover {
            border-color: #FF0050;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 30px;
        }
        
        QComboBox::down-arrow {
            image: url(assets/icons/arrow_down_white.png);
            width: 12px;
            height: 12px;
        }
        
        QComboBox QAbstractItemView {
            background-color: #161823;
            border: 2px solid #2F2F35;
            border-radius: 8px;
            color: #FFFFFF;
            selection-background-color: #FF0050;
        }
        
        /* Spin Box */
        QSpinBox, QDoubleSpinBox {
            background-color: #161823;
            border: 2px solid #2F2F35;
            border-radius: 8px;
            padding: 12px;
            color: #FFFFFF;
            font-size: 14px;
        }
        
        QSpinBox:focus, QDoubleSpinBox:focus {
            border-color: #FF0050;
        }
        
        /* Progress Bar */
        QProgressBar {
            background-color: #161823;
            border: 2px solid #2F2F35;
            border-radius: 8px;
            text-align: center;
            color: #FFFFFF;
            font-weight: 600;
        }
        
        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #FF0050, stop:1 #25F4EE);
            border-radius: 6px;
        }
        
        /* Labels */
        QLabel {
            color: #FFFFFF;
            font-size: 14px;
        }
        
        QLabel[class="title"] {
            font-size: 24px;
            font-weight: 700;
            color: #FF0050;
        }
        
        QLabel[class="subtitle"] {
            font-size: 18px;
            font-weight: 600;
            color: #25F4EE;
        }
        
        QLabel[class="description"] {
            color: #A8A8B3;
            font-size: 12px;
        }
        
        /* Group Box */
        QGroupBox {
            background-color: #161823;
            border: 2px solid #2F2F35;
            border-radius: 12px;
            margin-top: 12px;
            padding-top: 12px;
            font-weight: 600;
            color: #FFFFFF;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
            color: #FF0050;
        }
        
        /* Tab Widget */
        QTabWidget::pane {
            background-color: #161823;
            border: 2px solid #2F2F35;
            border-radius: 8px;
        }
        
        QTabBar::tab {
            background-color: #2F2F35;
            color: #A8A8B3;
            padding: 12px 24px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        
        QTabBar::tab:selected {
            background-color: #FF0050;
            color: #FFFFFF;
        }
        
        QTabBar::tab:hover {
            background-color: #25F4EE;
            color: #000000;
        }
        
        /* Scroll Bar */
        QScrollBar:vertical {
            background-color: #161823;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #FF0050;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #E6004A;
        }
        
        /* Menu Bar */
        QMenuBar {
            background-color: #161823;
            color: #FFFFFF;
            border-bottom: 2px solid #2F2F35;
        }
        
        QMenuBar::item {
            padding: 8px 16px;
            background-color: transparent;
        }
        
        QMenuBar::item:selected {
            background-color: #FF0050;
        }
        
        /* Status Bar */
        QStatusBar {
            background-color: #161823;
            color: #A8A8B3;
            border-top: 2px solid #2F2F35;
        }
        
        /* Tool Tip */
        QToolTip {
            background-color: #1F1F23;
            color: #FFFFFF;
            border: 2px solid #FF0050;
            border-radius: 8px;
            padding: 8px;
            font-size: 12px;
        }
        """
    
    def _get_light_stylesheet(self) -> str:
        """Get light theme stylesheet."""
        # Similar structure but with light colors
        return """
        /* Main Window */
        QMainWindow {
            background-color: #FFFFFF;
            color: #161823;
        }
        
        /* Central Widget */
        QWidget {
            background-color: #FFFFFF;
            color: #161823;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        /* Buttons */
        QPushButton {
            background-color: #FF0050;
            color: #FFFFFF;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            min-height: 20px;
        }
        
        QPushButton:hover {
            background-color: #E6004A;
        }
        
        QPushButton:pressed {
            background-color: #CC0042;
        }
        
        QPushButton:disabled {
            background-color: #E5E7EB;
            color: #6B7280;
        }
        
        /* Input Fields */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #F8F8F8;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            padding: 12px;
            color: #161823;
            font-size: 14px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #FF0050;
        }
        
        /* Labels */
        QLabel {
            color: #161823;
            font-size: 14px;
        }
        
        QLabel[class="title"] {
            font-size: 24px;
            font-weight: 700;
            color: #FF0050;
        }
        
        QLabel[class="subtitle"] {
            font-size: 18px;
            font-weight: 600;
            color: #25F4EE;
        }
        
        QLabel[class="description"] {
            color: #6B7280;
            font-size: 12px;
        }
        """
    
    def set_theme(self, theme_name: str):
        """Set application theme.
        
        Args:
            theme_name: Name of theme to apply ('dark' or 'light')
        """
        if theme_name not in self.themes:
            theme_name = "dark"  # Default to dark theme
        
        self.current_theme = theme_name
        theme = self.themes[theme_name]
        
        # Apply stylesheet
        self.app.setStyleSheet(theme["stylesheet"])
        
        # Emit theme changed signal
        self.theme_changed.emit(theme_name)
    
    def get_current_theme(self) -> str:
        """Get current theme name."""
        return self.current_theme
    
    def get_theme_colors(self, theme_name: str = None) -> Dict[str, str]:
        """Get theme colors.
        
        Args:
            theme_name: Theme name (uses current theme if None)
            
        Returns:
            Dictionary of color values
        """
        if theme_name is None:
            theme_name = self.current_theme
        
        return self.themes.get(theme_name, self.themes["dark"])["colors"]
    
    def toggle_theme(self):
        """Toggle between dark and light themes."""
        new_theme = "light" if self.current_theme == "dark" else "dark"
        self.set_theme(new_theme)
