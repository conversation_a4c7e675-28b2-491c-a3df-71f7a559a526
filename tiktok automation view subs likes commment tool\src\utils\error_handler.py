"""
Professional Error Handler for TikTok Automation Tool
Comprehensive error handling, reporting, and recovery mechanisms.
"""

import sys
import traceback
import logging
import time
from typing import Optional, Dict, Any, Callable
from pathlib import Path
from functools import wraps

from PyQt5.QtWidgets import QMessageBox, QApplication
from PyQt5.QtCore import QObject, pyqtSignal

class ErrorSeverity:
    """Error severity levels."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class ErrorHandler(QObject):
    """Professional error handler with reporting and recovery."""
    
    error_occurred = pyqtSignal(str, str, str)  # severity, title, message
    
    def __init__(self, log_dir: Path = None):
        """Initialize error handler.
        
        Args:
            log_dir: Directory for error logs
        """
        super().__init__()
        
        self.log_dir = log_dir or Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        self.error_log_file = self.log_dir / f"errors_{time.strftime('%Y%m%d')}.log"
        self.crash_log_file = self.log_dir / f"crashes_{time.strftime('%Y%m%d')}.log"
        
        # Error statistics
        self.error_count = 0
        self.error_history = []
        
        # Recovery strategies
        self.recovery_strategies = {}
        
        # Setup logging
        self._setup_error_logging()
        
        logging.info("Error handler initialized")
    
    def _setup_error_logging(self):
        """Setup error-specific logging."""
        # Create error logger
        self.error_logger = logging.getLogger('error_handler')
        self.error_logger.setLevel(logging.ERROR)
        
        # Create file handler for errors
        error_handler = logging.FileHandler(self.error_log_file, encoding='utf-8')
        error_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        error_handler.setFormatter(error_formatter)
        self.error_logger.addHandler(error_handler)
    
    def handle_exception(self, exc_type, exc_value, exc_traceback, severity: str = ErrorSeverity.HIGH):
        """Handle uncaught exceptions.
        
        Args:
            exc_type: Exception type
            exc_value: Exception value
            exc_traceback: Exception traceback
            severity: Error severity level
        """
        if issubclass(exc_type, KeyboardInterrupt):
            # Handle Ctrl+C gracefully
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # Format error information
        error_info = {
            'type': exc_type.__name__,
            'message': str(exc_value),
            'traceback': ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)),
            'timestamp': time.time(),
            'severity': severity
        }
        
        # Log error
        self._log_error(error_info)
        
        # Update statistics
        self.error_count += 1
        self.error_history.append(error_info)
        
        # Show user-friendly error dialog
        self._show_error_dialog(error_info)
        
        # Emit signal for UI updates
        self.error_occurred.emit(
            severity,
            f"{exc_type.__name__}",
            f"An error occurred: {str(exc_value)}"
        )
        
        # Attempt recovery if strategy exists
        self._attempt_recovery(exc_type.__name__, error_info)
    
    def handle_error(self, error: Exception, context: str = "", severity: str = ErrorSeverity.MEDIUM, show_dialog: bool = True):
        """Handle application errors with context.
        
        Args:
            error: Exception object
            context: Context where error occurred
            severity: Error severity level
            show_dialog: Whether to show error dialog
        """
        error_info = {
            'type': type(error).__name__,
            'message': str(error),
            'context': context,
            'traceback': traceback.format_exc(),
            'timestamp': time.time(),
            'severity': severity
        }
        
        # Log error
        self._log_error(error_info)
        
        # Update statistics
        self.error_count += 1
        self.error_history.append(error_info)
        
        # Show dialog if requested
        if show_dialog:
            self._show_error_dialog(error_info)
        
        # Emit signal
        self.error_occurred.emit(
            severity,
            f"Error in {context}" if context else "Application Error",
            str(error)
        )
        
        # Attempt recovery
        self._attempt_recovery(type(error).__name__, error_info)
    
    def _log_error(self, error_info: Dict[str, Any]):
        """Log error information.
        
        Args:
            error_info: Error information dictionary
        """
        log_message = (
            f"ERROR: {error_info['type']} - {error_info['message']}\n"
            f"Context: {error_info.get('context', 'N/A')}\n"
            f"Severity: {error_info['severity']}\n"
            f"Traceback:\n{error_info['traceback']}"
        )
        
        self.error_logger.error(log_message)
        
        # Also log to main logger
        logging.error(f"{error_info['type']}: {error_info['message']}")
    
    def _show_error_dialog(self, error_info: Dict[str, Any]):
        """Show user-friendly error dialog.
        
        Args:
            error_info: Error information dictionary
        """
        try:
            app = QApplication.instance()
            if not app:
                return
            
            # Determine dialog type based on severity
            if error_info['severity'] == ErrorSeverity.CRITICAL:
                icon = QMessageBox.Critical
                title = "Critical Error"
            elif error_info['severity'] == ErrorSeverity.HIGH:
                icon = QMessageBox.Warning
                title = "Error"
            else:
                icon = QMessageBox.Information
                title = "Notice"
            
            # Create user-friendly message
            user_message = self._create_user_friendly_message(error_info)
            
            # Show dialog
            msg_box = QMessageBox()
            msg_box.setIcon(icon)
            msg_box.setWindowTitle(title)
            msg_box.setText(user_message)
            
            # Add details button for technical information
            msg_box.setDetailedText(error_info['traceback'])
            
            # Add buttons
            msg_box.setStandardButtons(QMessageBox.Ok)
            
            if error_info['severity'] in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                msg_box.addButton("Report Bug", QMessageBox.ActionRole)
                msg_box.addButton("Restart Application", QMessageBox.ActionRole)
            
            result = msg_box.exec_()
            
            # Handle button clicks
            if msg_box.clickedButton().text() == "Report Bug":
                self._open_bug_report(error_info)
            elif msg_box.clickedButton().text() == "Restart Application":
                self._restart_application()
        
        except Exception as e:
            # Fallback error handling
            logging.error(f"Failed to show error dialog: {e}")
    
    def _create_user_friendly_message(self, error_info: Dict[str, Any]) -> str:
        """Create user-friendly error message.
        
        Args:
            error_info: Error information dictionary
            
        Returns:
            User-friendly error message
        """
        error_type = error_info['type']
        context = error_info.get('context', '')
        
        # Common error messages
        friendly_messages = {
            'ConnectionError': "Unable to connect to the internet or TikTok servers. Please check your connection.",
            'TimeoutError': "The operation took too long to complete. Please try again.",
            'PermissionError': "Permission denied. Please run the application as administrator.",
            'FileNotFoundError': "A required file was not found. Please reinstall the application.",
            'ImportError': "A required component is missing. Please reinstall the application.",
            'ValueError': "Invalid input provided. Please check your settings.",
            'KeyError': "Configuration error. Please reset your settings.",
            'AttributeError': "Internal error occurred. Please restart the application.",
            'TypeError': "Data type error. Please check your input.",
            'OSError': "System error occurred. Please check your system configuration."
        }
        
        base_message = friendly_messages.get(error_type, f"An unexpected error occurred: {error_info['message']}")
        
        if context:
            return f"{base_message}\n\nContext: {context}"
        
        return base_message
    
    def _attempt_recovery(self, error_type: str, error_info: Dict[str, Any]):
        """Attempt to recover from error.
        
        Args:
            error_type: Type of error
            error_info: Error information
        """
        if error_type in self.recovery_strategies:
            try:
                recovery_func = self.recovery_strategies[error_type]
                recovery_func(error_info)
                logging.info(f"Recovery attempted for {error_type}")
            except Exception as e:
                logging.error(f"Recovery failed for {error_type}: {e}")
    
    def register_recovery_strategy(self, error_type: str, recovery_func: Callable):
        """Register recovery strategy for error type.
        
        Args:
            error_type: Error type name
            recovery_func: Recovery function
        """
        self.recovery_strategies[error_type] = recovery_func
        logging.info(f"Recovery strategy registered for {error_type}")
    
    def _open_bug_report(self, error_info: Dict[str, Any]):
        """Open bug report interface.
        
        Args:
            error_info: Error information
        """
        try:
            # Create bug report data
            bug_report = {
                'error_type': error_info['type'],
                'error_message': error_info['message'],
                'context': error_info.get('context', ''),
                'timestamp': error_info['timestamp'],
                'traceback': error_info['traceback']
            }
            
            # Save bug report to file
            bug_report_file = self.log_dir / f"bug_report_{int(time.time())}.json"
            
            import json
            with open(bug_report_file, 'w', encoding='utf-8') as f:
                json.dump(bug_report, f, indent=2, ensure_ascii=False)
            
            QMessageBox.information(
                None,
                "Bug Report",
                f"Bug report saved to:\n{bug_report_file}\n\n"
                "Please send this file to support for assistance."
            )
            
        except Exception as e:
            logging.error(f"Failed to create bug report: {e}")
    
    def _restart_application(self):
        """Restart the application."""
        try:
            import os
            import sys
            
            QMessageBox.information(
                None,
                "Restart",
                "The application will now restart."
            )
            
            # Close current application
            app = QApplication.instance()
            if app:
                app.quit()
            
            # Restart
            os.execl(sys.executable, sys.executable, *sys.argv)
            
        except Exception as e:
            logging.error(f"Failed to restart application: {e}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics.
        
        Returns:
            Dictionary containing error statistics
        """
        if not self.error_history:
            return {
                'total_errors': 0,
                'error_types': {},
                'severity_distribution': {},
                'recent_errors': []
            }
        
        # Count error types
        error_types = {}
        severity_distribution = {}
        
        for error in self.error_history:
            error_type = error['type']
            severity = error['severity']
            
            error_types[error_type] = error_types.get(error_type, 0) + 1
            severity_distribution[severity] = severity_distribution.get(severity, 0) + 1
        
        # Get recent errors (last 10)
        recent_errors = self.error_history[-10:] if len(self.error_history) > 10 else self.error_history
        
        return {
            'total_errors': len(self.error_history),
            'error_types': error_types,
            'severity_distribution': severity_distribution,
            'recent_errors': [
                {
                    'type': error['type'],
                    'message': error['message'],
                    'timestamp': error['timestamp'],
                    'severity': error['severity']
                }
                for error in recent_errors
            ]
        }
    
    def clear_error_history(self):
        """Clear error history."""
        self.error_history.clear()
        self.error_count = 0
        logging.info("Error history cleared")

def error_handler_decorator(severity: str = ErrorSeverity.MEDIUM, show_dialog: bool = True):
    """Decorator for automatic error handling.
    
    Args:
        severity: Error severity level
        show_dialog: Whether to show error dialog
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Get global error handler if available
                error_handler = getattr(QApplication.instance(), 'error_handler', None)
                if error_handler:
                    error_handler.handle_error(
                        e, 
                        context=f"{func.__module__}.{func.__name__}",
                        severity=severity,
                        show_dialog=show_dialog
                    )
                else:
                    # Fallback logging
                    logging.error(f"Error in {func.__name__}: {e}")
                    if show_dialog:
                        QMessageBox.critical(None, "Error", str(e))
                
                return None
        return wrapper
    return decorator

def setup_global_error_handler(app: QApplication, log_dir: Path = None):
    """Setup global error handler for application.
    
    Args:
        app: QApplication instance
        log_dir: Directory for error logs
    """
    error_handler = ErrorHandler(log_dir)
    
    # Set as application attribute
    app.error_handler = error_handler
    
    # Set as global exception handler
    def handle_exception(exc_type, exc_value, exc_traceback):
        error_handler.handle_exception(exc_type, exc_value, exc_traceback)
    
    sys.excepthook = handle_exception
    
    logging.info("Global error handler setup complete")
