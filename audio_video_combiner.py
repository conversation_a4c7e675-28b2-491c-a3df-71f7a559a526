#!/usr/bin/env python3
"""
Audio Video Combiner - Combines TTS audio with video files
Uses moviepy as a Python-only solution (no FFmpeg required)
"""

import os
import tempfile
import time
from pathlib import Path

# Try to import moviepy
try:
    from moviepy.editor import VideoFileClip, AudioFileClip, CompositeAudioClip
    HAS_MOVIEPY = True
    print("✅ MoviePy available for video processing")
except ImportError:
    HAS_MOVIEPY = False
    print("⚠️ MoviePy not available - installing...")

def install_moviepy():
    """Install moviepy for video processing"""
    try:
        import subprocess
        import sys
        
        print("📦 Installing MoviePy...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'moviepy'
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ MoviePy installed successfully!")
            return True
        else:
            print(f"❌ MoviePy installation failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error installing MoviePy: {e}")
        return False

class AudioVideoCombiner:
    """
    Combines TTS audio with video files using MoviePy
    """
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="video_audio_")
        
        # Check if moviepy is available
        global HAS_MOVIEPY
        if not HAS_MOVIEPY:
            if install_moviepy():
                try:
                    from moviepy.editor import VideoFileClip, AudioFileClip, CompositeAudioClip
                    HAS_MOVIEPY = True
                    print("✅ MoviePy now available!")
                except ImportError:
                    print("❌ MoviePy still not available after installation")
    
    def combine_audio_with_video(self, video_path, audio_segments, output_path, 
                                background_volume=0.3, tts_volume=1.0):
        """
        Combine TTS audio segments with video
        
        Args:
            video_path: Path to input video
            audio_segments: List of audio segments with timing
            output_path: Path for output video
            background_volume: Volume of original video audio (0.0-1.0)
            tts_volume: Volume of TTS audio (0.0-1.0)
        """
        
        if not HAS_MOVIEPY:
            print("❌ MoviePy not available - cannot combine audio with video")
            return self._fallback_method(video_path, audio_segments, output_path)
        
        try:
            print(f"🎬 Loading video: {video_path}")
            
            # Load the video
            video = VideoFileClip(video_path)
            video_duration = video.duration
            
            print(f"📹 Video duration: {video_duration:.1f} seconds")
            print(f"🎤 Processing {len(audio_segments)} audio segments")
            
            # Create list of audio clips
            audio_clips = []
            
            # Add original video audio (reduced volume)
            if video.audio is not None:
                original_audio = video.audio.volumex(background_volume)
                audio_clips.append(original_audio)
                print(f"🔊 Original audio volume: {background_volume}")
            
            # Add TTS audio segments
            for i, segment in enumerate(audio_segments):
                if not os.path.exists(segment['file']):
                    print(f"⚠️ Audio file not found: {segment['file']}")
                    continue
                
                try:
                    # Load TTS audio
                    tts_audio = AudioFileClip(segment['file'])
                    
                    # Set volume
                    tts_audio = tts_audio.volumex(tts_volume)
                    
                    # Set timing
                    start_time = segment.get('start_time', 0)
                    tts_audio = tts_audio.set_start(start_time)
                    
                    audio_clips.append(tts_audio)
                    
                    print(f"🎤 Added TTS {i+1}: {start_time:.1f}s - {segment['text'][:30]}...")
                    
                except Exception as e:
                    print(f"⚠️ Error processing audio segment {i+1}: {e}")
                    continue
            
            if not audio_clips:
                print("❌ No audio clips to process")
                return False
            
            # Combine all audio
            print("🔄 Combining audio tracks...")
            final_audio = CompositeAudioClip(audio_clips)
            
            # Set the combined audio to the video
            final_video = video.set_audio(final_audio)
            
            # Write the result
            print(f"💾 Writing output video: {output_path}")
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=os.path.join(self.temp_dir, 'temp_audio.m4a'),
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # Clean up
            video.close()
            final_audio.close()
            final_video.close()
            
            print(f"✅ Video with TTS audio created: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Error combining audio with video: {e}")
            return False
    
    def _fallback_method(self, video_path, audio_segments, output_path):
        """
        Fallback method when MoviePy is not available
        Just copies the original video and creates separate audio
        """
        try:
            print("⚠️ Using fallback method - copying original video")
            
            # Copy original video
            import shutil
            shutil.copy2(video_path, output_path)
            
            # Create combined audio file
            if audio_segments:
                audio_output = output_path.replace('.mp4', '_tts_audio.wav')
                
                # For now, just copy the first audio file
                if os.path.exists(audio_segments[0]['file']):
                    shutil.copy2(audio_segments[0]['file'], audio_output)
                    print(f"📁 TTS audio saved separately: {audio_output}")
            
            print("⚠️ Video copied without audio integration")
            print("💡 Install MoviePy for full audio integration: pip install moviepy")
            return True
            
        except Exception as e:
            print(f"❌ Fallback method failed: {e}")
            return False
    
    def get_video_info(self, video_path):
        """Get information about a video file"""
        if not HAS_MOVIEPY:
            return {"duration": 60, "fps": 30, "resolution": "unknown"}
        
        try:
            video = VideoFileClip(video_path)
            info = {
                "duration": video.duration,
                "fps": video.fps,
                "resolution": f"{video.w}x{video.h}",
                "has_audio": video.audio is not None
            }
            video.close()
            return info
        except Exception as e:
            print(f"⚠️ Could not get video info: {e}")
            return {"duration": 60, "fps": 30, "resolution": "unknown"}
    
    def cleanup(self):
        """Clean up temporary files"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            print("🧹 Temporary files cleaned up")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")

# Test function
def test_combiner():
    """Test the audio video combiner"""
    combiner = AudioVideoCombiner()
    
    print("🧪 Testing Audio Video Combiner")
    print(f"MoviePy available: {HAS_MOVIEPY}")
    
    if HAS_MOVIEPY:
        print("✅ Ready for full video + audio integration")
    else:
        print("⚠️ Limited functionality - install MoviePy for full features")
    
    combiner.cleanup()

if __name__ == "__main__":
    test_combiner()
